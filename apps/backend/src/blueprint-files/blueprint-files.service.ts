import {
  Injectable,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import {
  BlueprintFiles,
  Takeoff,
  BlueprintImage,
} from '../../prisma/app/generated/prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { BlueprintFileDto } from 'src/takeoff/dto/blue-print-file.dto';
import { PaginationService } from 'src/pagination/pagination.service';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { BlueprintImageUpdateDto } from './dto/blueprint-image-update.dto';
import * as pdfParse from 'pdf-parse';

@Injectable()
export class BlueprintFilesService {
  constructor(
    private prismaService: PrismaService,
    private paginationService: PaginationService,
    private fileUploadService: FileUploadService,
    @InjectQueue('pdf-processing') private pdfQueue: Queue,
  ) {}

  async create(takeoffId: number, files: Array<Express.Multer.File>) {
    const takeoff = await this.prismaService.takeoff.findUnique({
      where: { id: takeoffId },
    });

    if (!takeoff) {
      throw new NotFoundException(`Takeoff with ID ${takeoffId} not found`);
    }

    const startTime = Date.now();
    let index = 0;
    for (const file of files) {
      try {
        const uploadResponse = await this.fileUploadService.uploadPublicFile(
          file.buffer,
          file.originalname,
          takeoff.userId,
        );
        const { key: awsKey } = uploadResponse;

        const iterationStartTime = Date.now();
        console.log(
          `Processing blueprint file ${index + 1} of ${files.length}`,
        );

        const totalPages = await this.getPdfPageCount(file.buffer);
        const blueprintFiles = await this.prismaService.blueprintFiles.create({
          data: {
            fileName: file.originalname,
            fileUrl: uploadResponse.file_url,
            takeoffId,
            awsKey: awsKey,
            totalPages,
          },
        });

        await this.convertPdfToImages(blueprintFiles.id, awsKey, totalPages);

        const iterationEndTime = Date.now();
        console.log(
          `Blueprint file processing took ${iterationEndTime - iterationStartTime}ms`,
        );
      } catch (error) {
        console.error(`Error processing blueprint file: ${error.message}`);
        throw new Error(`Failed to process blueprint file: ${error.message}`);
      }
      index++;
    }
    const totalTime = Date.now() - startTime;
    console.log(`Total processing time: ${totalTime}ms`);

    return {
      message: 'Blueprint files created successfully',
    };
  }

  async findAllBluePrintImagesByBlueprintFileId(
    blueprintFileId: string,
    query: PaginateQuery,
  ) {
    await this.findOne(blueprintFileId);
    return this.paginationService.paginate<BlueprintImage>('blueprintImage', {
      page: query.page,
      perPage: query.perPage,
      where: { blueprintFileId },
      orderBy: {
        pageNumber: 'asc',
      },
    });
  }

  async findBluePrintImageById(blueprintImageId: string, authUserId: string) {
    const blueprintImage = await this.prismaService.blueprintImage.findUnique({
      where: { id: blueprintImageId },
      include: {
        blueprintFile: {
          include: {
            takeoff: true,
          },
        },
      },
    });
    if (!blueprintImage) {
      throw new NotFoundException(
        `Blueprint image with ID ${blueprintImageId} not found`,
      );
    }
    if (blueprintImage.blueprintFile.takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not allowed to access this resource',
      );
    }
    return blueprintImage;
  }

  async updateBlueprintImage(
    blueprintImageId: string,
    authUserId: string,
    blueprintImageUpdateDto: BlueprintImageUpdateDto,
  ) {
    await this.findBluePrintImageById(blueprintImageId, authUserId);
    const { scale, dimensions, filename } = blueprintImageUpdateDto;

    // Prepare data object with only the fields that are provided
    const updateData: any = {};
    if (scale !== undefined) updateData.scale = scale;
    if (dimensions !== undefined) updateData.dimensions = dimensions;
    if (filename !== undefined) updateData.filename = filename;

    const blueprintImage = await this.prismaService.blueprintImage.update({
      where: {
        id: blueprintImageId,
      },
      data: updateData,
    });

    return {
      message: 'Blueprint image updated successfully',
      data: blueprintImage,
    };
  }

  async findByTakeoffId(
    takeoffId: number,
    query: PaginateQuery,
    authUserId: string,
  ) {
    const takeoff = await this.prismaService.takeoff.findUnique({
      where: { id: takeoffId },
    });

    if (!takeoff) {
      throw new NotFoundException(`Takeoff with ID ${takeoffId} not found`);
    }
    if (takeoff.userId !== authUserId) {
      throw new ForbiddenException(
        'You are not allowed to access this resource',
      );
    }
    const { page, perPage } = query;

    return this.paginationService.paginate<BlueprintFiles>('blueprintFiles', {
      page,
      perPage,
      where: {
        takeoffId,
        deletedAt: null,
      },
      include: {
        takeoff: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string): Promise<BlueprintFiles & { takeoff: Takeoff }> {
    const blueprintFile = await this.prismaService.blueprintFiles.findUnique({
      where: { id },
      include: {
        takeoff: true,
      },
    });
    if (!blueprintFile || blueprintFile.deletedAt) {
      throw new NotFoundException(`Blueprint file with ID ${id} not found`);
    }

    return blueprintFile;
  }

  async update(id: string, dto: Partial<BlueprintFileDto>) {
    await this.findOne(id);
    const blueprintFile = await this.prismaService.blueprintFiles.update({
      where: { id },
      data: dto,
    });

    return {
      message: 'Blueprint file updated successfully',
      data: blueprintFile,
    };
  }

  async softDelete(id: string) {
    await this.findOne(id);
    const blueprintFile = await this.prismaService.blueprintFiles.update({
      where: { id },
      data: { deletedAt: new Date() },
    });

    return {
      message: 'Blueprint file deleted successfully',
      data: blueprintFile,
    };
  }

  async hardDelete(id: string) {
    await this.findOne(id);
    await this.prismaService.blueprintFiles.delete({
      where: { id },
    });

    return {
      message: 'Blueprint file deleted successfully',
      data: null,
    };
  }

  async convertPdfToImages(
    blueprintFileId: string,
    pdfAwsKey: string,
    totalPages: number,
  ) {
    const blueprintFile = await this.findOne(blueprintFileId);
    const uploaderId = blueprintFile.takeoff.userId;
    const BATCH_SIZE = Math.min(5, totalPages); // Number of pages to process in each batch
    // Queue batches for background processing
    if (totalPages >= BATCH_SIZE) {
      console.log('Queueing remaining batches for background processing...');
      const queuePromises = [];
      for (
        let startPage = 1;
        startPage <= totalPages;
        startPage += BATCH_SIZE
      ) {
        const endPage = Math.min(startPage + BATCH_SIZE - 1, totalPages);
        const jobOptions = {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: false,
          removeOnFail: false,
        };

        const queuePromise = this.pdfQueue.add(
          'convert-pdf-batch',
          {
            blueprintFileId,
            awsKey: pdfAwsKey,
            startPage,
            endPage,
            uploaderId,
            fileName: blueprintFile.fileName,
          },
          jobOptions,
        );

        queuePromises.push(queuePromise);
      }

      try {
        await Promise.all(queuePromises);
        console.log('All remaining batches queued successfully');
      } catch (error) {
        console.error('Error queueing batch jobs:', error);
        throw new Error(
          `Failed to queue background processing jobs: ${error.message}`,
        );
      }
    }

    return {
      message: `${totalPages} pages will be processed in the background.`,
      data: {
        total: totalPages,
        remaining: Math.max(0, totalPages),
      },
    };
  }

  private async getPdfPageCount(pdfBuffer: Buffer): Promise<number> {
    // Get total number of pages
    const totalPages = (await pdfParse(pdfBuffer)).numpages;
    console.log(`Total pages in PDF: ${totalPages}`);
    return totalPages;
  }
}
