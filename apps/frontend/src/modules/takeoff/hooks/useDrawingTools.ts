import { useState, useCallback } from 'react';
import Kon<PERSON> from 'konva';
import { useQueryClient } from '@tanstack/react-query';
import { DrawingShape, SelectedTool } from '../types/drawing-types';
import {
  getRelativePointerPosition,
  generateId,
  calculateRadius,
  calculateEllipseRadii,
  isDrawingInSelectionBox,
  isDrawingWithinBounds,
  hasSelfintersection,
} from '../utils/canvas-utils';
import { useTakeoffStore } from '../store/takeoff-store';
import { useCreateDrawing, useUpdateDrawing } from '../api/mutations';
import {
  CreateDrawingPayload,
  UpdateDrawingPayload,
  Drawing,
} from '../types/drawing';
import { getPointShapeGenerator } from '../utils/point-shapes';
import {
  STROKE_WIDTHS,
  DRAWING_COLORS,
  ARROW_POINTER,
  DEFAULT_POINT_RADIUS,
  DEFAULT_EDGE_THICKNESS,
} from '../constants/drawing';
import { queryKeys } from '@/lib/query-keys';
import { toast } from '@/lib/toast';
import { CanvasDimensions } from '../types/drawing-types';
import { useUndoRedo } from './useUndoRedo';
import { useShiftKey } from './useShiftKey';
import { usePanningConstraints } from './usePanningConstraints';
import { getMeasureToolProperties } from '../constants/measure-tools';
import { snapPointTo90Degrees } from '../utils/angle-snap-utils';
interface UseDrawingToolsProps {
  zoom: number; // for zoom/transform
  position: { x: number; y: number };
  setPosition: React.Dispatch<React.SetStateAction<{ x: number; y: number }>>;
  fetchedDrawings?: Drawing[];
  dimensions: CanvasDimensions;
  viewportDimensions: CanvasDimensions;
}

interface UseDrawingToolsReturn {
  selectedTool: SelectedTool;
  setSelectedTool: React.Dispatch<React.SetStateAction<SelectedTool>>;
  currentDrawing: DrawingShape | null;
  isShapeDragging: boolean;
  setIsShapeDragging: React.Dispatch<React.SetStateAction<boolean>>;
  isGroupDragging: boolean;
  setIsGroupDragging: React.Dispatch<React.SetStateAction<boolean>>;
  groupDragOffset: { x: number; y: number } | null;
  isSpacebarPanning: boolean;
  setIsSpacebarPanning: React.Dispatch<React.SetStateAction<boolean>>;
  isMiddleMousePanning: boolean;
  setIsMiddleMousePanning: React.Dispatch<React.SetStateAction<boolean>>;
  showCommentForm: boolean;
  commentFormPosition: { x: number; y: number };
  handleCommentSave: (text: string) => void;
  handleCommentCancel: () => void;
  handleMouseDown: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  handleMouseMove: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  handleMouseUp: (e?: Konva.KonvaEventObject<MouseEvent>) => void;
  handleDragEnd: (
    drawingId: number,
    event: Konva.KonvaEventObject<DragEvent>,
  ) => void;
  handleGroupDragStart: () => void;
  handleGroupDragMove: (deltaX: number, deltaY: number) => void;
  handleGroupDragEnd: (deltaX: number, deltaY: number) => void;
  handleDrawingSelect: (drawingId: number) => void;
  handleStageClick: (e: Konva.KonvaEventObject<MouseEvent>) => void;
  cancelCurrentDrawing: () => void;
}

/**
 * Hook to manage drawing tools and operations
 */
export function useDrawingTools({
  zoom,
  position,
  setPosition,
  fetchedDrawings = [],
  dimensions,
  viewportDimensions,
}: UseDrawingToolsProps): UseDrawingToolsReturn {
  const [selectedTool, setSelectedTool] = useState<SelectedTool>('pan');
  const [currentDrawing, setCurrentDrawing] = useState<DrawingShape | null>(
    null,
  );
  const [isShapeDragging, setIsShapeDragging] = useState(false);
  const [isGroupDragging, setIsGroupDragging] = useState(false);
  const [groupDragOffset, setGroupDragOffset] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [isSpacebarPanning, setIsSpacebarPanning] = useState(false);
  const [isMiddleMousePanning, setIsMiddleMousePanning] = useState(false);
  const [isMiddleMouseDragging, setIsMiddleMouseDragging] = useState(false);
  const [middleMouseStartPos, setMiddleMouseStartPos] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [lastClickTime, setLastClickTime] = useState<number | null>(null);
  const [shiftPressedDuringDrag, setShiftPressedDuringDrag] = useState(false);

  // Comment form state
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [commentFormPosition, setCommentFormPosition] = useState({
    x: 0,
    y: 0,
  });
  const [commentCanvasPosition, setCommentCanvasPosition] = useState({
    x: 0,
    y: 0,
  });

  // API Hooks and Store Data
  const {
    selectedImage: storeSelectedImage,
    selectedComponentItem,
    selectedDrawingId,
    setSelectedDrawingId,
    setSelectedDrawingIds,
    selectedDrawingIds,
    clearSelection,
    isEditMode,
    isShiftPressed,
    selectedMeasureTool,
    isMeasureMode,
  } = useTakeoffStore();
  const createDrawingMutation = useCreateDrawing();
  const updateDrawingMutation = useUpdateDrawing();
  const queryClient = useQueryClient();
  const { recordAction } = useUndoRedo();

  const currentBlueprintImageId = storeSelectedImage?.id;

  // Initialize shift key detection hook
  useShiftKey();

  // Initialize panning constraints for middle mouse button panning
  const panningConstraints = usePanningConstraints({
    imageDimensions: dimensions,
    viewportDimensions,
    scale: zoom,
    setPosition,
  });

  // Helper function to apply constraints to position updates
  const applyConstraints = useCallback(
    (newPosition: { x: number; y: number }): { x: number; y: number } => {
      return panningConstraints.constrainPosition(newPosition);
    },
    [panningConstraints],
  );

  // Handle mouse down event for drawing
  const handleMouseDown = (e: Konva.KonvaEventObject<MouseEvent>) => {
    // Handle middle mouse button (button 1) for panning
    if (e.evt.button === 1) {
      e.evt.preventDefault();
      setIsMiddleMousePanning(true);
      setIsMiddleMouseDragging(true);
      // Store the initial mouse position
      setMiddleMouseStartPos({ x: e.evt.clientX, y: e.evt.clientY });
      return;
    }

    // Only handle left mouse button (button 0), ignore right-clicks
    if (e.evt.button !== 0) {
      return;
    }

    // Prevent starting new drawings/selections if there's already a current drawing
    // This helps prevent conflicts with paste operations and other interactions
    if (currentDrawing) {
      return;
    }

    // In view mode, only allow pan tool operations
    if (!isEditMode && selectedTool !== 'pan') {
      return;
    }

    if (selectedTool === 'pan') {
      // If in pan mode, stage dragging is handled by the `draggable` prop on Stage.
      return;
    }

    if (selectedTool === 'select') {
      // If spacebar is pressed or middle mouse button is held while in select mode, act as pan
      if (isSpacebarPanning || isMiddleMousePanning) {
        // Stage dragging is handled by the `draggable` prop on Stage.
        return;
      }

      // If in select mode, start selection box
      const stage = e.target.getStage();
      if (!stage) return;

      const pos = getRelativePointerPosition(stage, position, zoom);
      if (!pos) return;

      // Track if shift was pressed when starting the selection
      setShiftPressedDuringDrag(isShiftPressed);

      const newSelectionId = generateId();
      setCurrentDrawing({
        id: newSelectionId,
        type: 'selection',
        x: pos.x,
        y: pos.y,
        width: 0,
        height: 0,
        fill: DRAWING_COLORS.MEASURE_SURFACE_FILL,
        stroke: DRAWING_COLORS.MEASURE_STROKE,
        strokeWidth: 1,
      });
      return;
    }

    // If spacebar is pressed or middle mouse button is held while using any tool, act as pan
    if (isSpacebarPanning || isMiddleMousePanning) {
      // Stage dragging should be handled by the Konva.Stage's `draggable` property,
      // which is expected to be set based on panning state in the component using this hook.
      return;
    }

    // For drawing tools ('rectangle', 'circle', 'freehand'):
    const stage = e.target.getStage();
    if (!stage) return;

    // Start a new drawing (we already checked currentDrawing is null above)
    const pos = getRelativePointerPosition(stage, position, zoom);
    if (!pos) return;

    const newShapeId = generateId();
    const baseShapeProps = { id: newShapeId, x: pos.x, y: pos.y };

    let shapeFillColor = '';
    if (selectedTool === 'rectangle') {
      if (isMeasureMode) {
        shapeFillColor = DRAWING_COLORS.MEASURE_FILL;
      } else {
        shapeFillColor = selectedComponentItem?.color
          ? `${selectedComponentItem.color}80`
          : '#e4423f80'; // Use component color with alpha, or default
      }
      setCurrentDrawing({
        ...baseShapeProps,
        type: 'rectangle',
        width: 0,
        height: 0,
        fill: shapeFillColor,
        stroke: DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: STROKE_WIDTHS.NORMAL,
      });
    } else if (selectedTool === 'circle') {
      if (isMeasureMode) {
        shapeFillColor = DRAWING_COLORS.MEASURE_FILL;
      } else {
        shapeFillColor = selectedComponentItem?.color
          ? `${selectedComponentItem.color}80`
          : '#3f83e480'; // Use component color with alpha, or default
      }
      setCurrentDrawing({
        ...baseShapeProps,
        type: 'circle',
        radius: 0,
        fill: shapeFillColor,
        stroke: DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: STROKE_WIDTHS.NORMAL,
      });
    } else if (selectedTool === 'ellipse') {
      if (isMeasureMode) {
        shapeFillColor = DRAWING_COLORS.MEASURE_FILL;
      } else {
        shapeFillColor = selectedComponentItem?.color
          ? `${selectedComponentItem.color}80`
          : '#3f83e480'; // Use component color with alpha, or default
      }
      setCurrentDrawing({
        ...baseShapeProps,
        type: 'ellipse',
        radiusX: 0,
        radiusY: 0,
        fill: shapeFillColor,
        stroke: DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: STROKE_WIDTHS.NORMAL,
      });
    } else if (selectedTool === 'freehand') {
      let isSurface = selectedComponentItem?.geometryType === 'surface';
      let strokeColor = selectedComponentItem?.color || 'black';

      // If in measure mode, use measure tool properties
      if (isMeasureMode) {
        const measureProps = getMeasureToolProperties(selectedMeasureTool);
        isSurface = measureProps.isSurface;
        if (measureProps.closed !== undefined) {
          isSurface = measureProps.closed;
        }
        strokeColor = 'black'; // Default for measure tools
      }

      shapeFillColor =
        isSurface && selectedComponentItem?.color
          ? `${selectedComponentItem.color}80`
          : isMeasureMode
            ? '#00000080' // Semi-transparent black for ALL measure tools
            : 'transparent';

      // Get edge thickness from selected component for edge drawings
      const edgeThickness =
        selectedComponentItem?.geometryType === 'edge'
          ? selectedComponentItem?.geometricData?.thickness ||
            DEFAULT_EDGE_THICKNESS
          : STROKE_WIDTHS.THICK;

      setCurrentDrawing({
        ...baseShapeProps,
        type: 'freehand',
        points: [pos.x, pos.y],
        closed: isSurface,
        tension: 0.3,
        fill: shapeFillColor,
        stroke: strokeColor,
        strokeWidth: isMeasureMode ? STROKE_WIDTHS.THICK : edgeThickness,
        thickness: isMeasureMode ? undefined : edgeThickness, // Store thickness for edge components
      });
    } else if (selectedTool === 'curve') {
      let isSurface = selectedComponentItem?.geometryType === 'surface';
      let strokeColor = selectedComponentItem?.color || 'black';

      // If in measure mode, use measure tool properties
      if (isMeasureMode) {
        const measureProps = getMeasureToolProperties(selectedMeasureTool);
        isSurface = measureProps.isSurface;
        if (measureProps.closed !== undefined) {
          isSurface = measureProps.closed;
        }
        strokeColor = 'black'; // Default for measure tools
      }

      const shapeFillColor =
        isSurface && selectedComponentItem?.color
          ? `${selectedComponentItem.color}80`
          : isMeasureMode
            ? DRAWING_COLORS.MEASURE_FILL
            : DRAWING_COLORS.TRANSPARENT_FILL;

      // Get edge thickness from selected component for edge drawings
      const edgeThickness =
        selectedComponentItem?.geometryType === 'edge'
          ? selectedComponentItem?.geometricData?.thickness ||
            DEFAULT_EDGE_THICKNESS
          : STROKE_WIDTHS.THICK;

      setCurrentDrawing({
        ...baseShapeProps,
        type: 'curve',
        points: [pos.x, pos.y], // Start with first anchor point
        previewPoints: [], // No preview initially, will be set on mouse move
        tension: 0.3, // Lower tension keeps curve closer to anchor points
        closed: isSurface,
        isDrawing: true,
        fill: shapeFillColor,
        stroke: strokeColor,
        strokeWidth: isMeasureMode ? STROKE_WIDTHS.THICK : edgeThickness,
        thickness: isMeasureMode ? undefined : edgeThickness, // Store thickness for edge components
      });
    } else if (selectedTool === 'point-to-point') {
      let isSurface = selectedComponentItem?.geometryType === 'surface';
      let strokeColor = selectedComponentItem?.color || 'black';

      // If in measure mode, use measure tool properties
      if (isMeasureMode) {
        const measureProps = getMeasureToolProperties(selectedMeasureTool);
        isSurface = measureProps.isSurface;
        if (measureProps.closed !== undefined) {
          isSurface = measureProps.closed;
        }
        strokeColor = 'black'; // Default for measure tools
      }

      const shapeFillColor =
        isSurface && selectedComponentItem?.color
          ? `${selectedComponentItem.color}80`
          : isMeasureMode
            ? '#00000080' // Semi-transparent black for ALL measure tools
            : 'transparent';

      // Get edge thickness from selected component for edge drawings
      const edgeThickness =
        selectedComponentItem?.geometryType === 'edge'
          ? selectedComponentItem?.geometricData?.thickness ||
            DEFAULT_EDGE_THICKNESS
          : STROKE_WIDTHS.THICK;

      setCurrentDrawing({
        ...baseShapeProps,
        type: 'point-to-point',
        points: [pos.x, pos.y], // Start with first anchor point
        previewPoints: [], // No preview initially, will be set on mouse move
        closed: isSurface,
        isDrawing: true,
        fill: shapeFillColor,
        stroke: strokeColor,
        strokeWidth: isMeasureMode ? STROKE_WIDTHS.THICK : edgeThickness,
        thickness: isMeasureMode ? undefined : edgeThickness, // Store thickness for edge components
      });
    } else if (selectedTool === 'arrow') {
      // Arrow tool - always black, no measurements
      setCurrentDrawing({
        ...baseShapeProps,
        type: 'arrow',
        points: [pos.x, pos.y], // Start with first anchor point
        previewPoints: [], // No preview initially, will be set on mouse move
        isDrawing: true,
        fill: 'black',
        stroke: 'black',
        strokeWidth: STROKE_WIDTHS.THICK,
        pointerLength: ARROW_POINTER.LENGTH,
        pointerWidth: ARROW_POINTER.WIDTH,
      });
    } else if (selectedTool === 'point') {
      const strokeColor = selectedComponentItem?.color || 'black';
      const fillColor = selectedComponentItem?.color
        ? `${selectedComponentItem.color}80`
        : DRAWING_COLORS.DEFAULT_STROKE;

      // Get point type from selected component, default to circle
      const pointType =
        selectedComponentItem?.geometricData?.pointType || 'circle';

      // Get point radius from selected component, default to 8
      const pointRadius =
        selectedComponentItem?.geometricData?.radius || DEFAULT_POINT_RADIUS;

      // Generate points for the shape using the radius from component data
      const generatePoints = getPointShapeGenerator(pointType);
      const points = generatePoints(pos.x, pos.y, pointRadius);

      setCurrentDrawing({
        ...baseShapeProps,
        type: 'point',
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: STROKE_WIDTHS.THICK,
        points,
        closed: true,
        pointType,
        radius: pointRadius, // Store the radius used for this drawing
      });

      // Immediately complete the point drawing (no mouse move/up needed)
      // We need to use a timeout to allow the state to update before calling handleMouseUp
      setTimeout(() => handleMouseUp(), 0);
    }
  };

  // Handle mouse move event for drawing
  const handleMouseMove = (e: Konva.KonvaEventObject<MouseEvent>) => {
    // Handle middle mouse button dragging for panning
    if (isMiddleMouseDragging && middleMouseStartPos) {
      const deltaX = e.evt.clientX - middleMouseStartPos.x;
      const deltaY = e.evt.clientY - middleMouseStartPos.y;

      setPosition((prevPos) => {
        const newPosition = {
          x: prevPos.x + deltaX,
          y: prevPos.y + deltaY,
        };
        return applyConstraints(newPosition);
      });

      // Update the start position for next move
      setMiddleMouseStartPos({ x: e.evt.clientX, y: e.evt.clientY });
      return;
    }

    if (selectedTool === 'pan' || !currentDrawing) {
      return;
    }

    // In view mode, don't allow drawing operations
    if (!isEditMode) {
      return;
    }

    // If spacebar is pressed or middle mouse button is held while using any tool, don't handle drawing
    if (isSpacebarPanning || isMiddleMousePanning) {
      // Drawing updates are paused.
      // Stage dragging should be handled by the Konva.Stage's `draggable` property.
      return;
    }

    const stage = e.target.getStage();
    const pos = getRelativePointerPosition(stage, position, zoom);
    if (!pos) return;

    if (currentDrawing.type === 'selection') {
      setCurrentDrawing({
        ...currentDrawing,
        width: pos.x - currentDrawing.x,
        height: pos.y - currentDrawing.y,
      });
    }

    if (currentDrawing.type === 'rectangle') {
      setCurrentDrawing({
        ...currentDrawing,
        width: pos.x - currentDrawing.x,
        height: pos.y - currentDrawing.y,
      });
    }

    if (currentDrawing.type === 'circle') {
      const radius = calculateRadius(
        currentDrawing.x,
        currentDrawing.y,
        pos.x,
        pos.y,
      );
      setCurrentDrawing({ ...currentDrawing, radius });
    }

    if (currentDrawing.type === 'ellipse') {
      const { radiusX, radiusY } = calculateEllipseRadii(
        currentDrawing.x,
        currentDrawing.y,
        pos.x,
        pos.y,
      );
      setCurrentDrawing({ ...currentDrawing, radiusX, radiusY });
    }

    if (currentDrawing.type === 'freehand') {
      // Add new point to the freehand line
      const newPoints = [...currentDrawing.points, pos.x, pos.y];
      setCurrentDrawing({
        ...currentDrawing,
        points: newPoints,
      });
    }

    if (currentDrawing.type === 'curve' && currentDrawing.isDrawing) {
      // Create complete preview including all anchor points + current mouse position
      // This shows the user exactly how the entire curve will look
      const completePreviewPoints = [...currentDrawing.points, pos.x, pos.y];

      setCurrentDrawing({
        ...currentDrawing,
        previewPoints: completePreviewPoints,
      });
    }

    if (currentDrawing.type === 'point-to-point' && currentDrawing.isDrawing) {
      // Determine target position - apply shift constraint if shift is pressed and we have at least one point
      let targetX = pos.x;
      let targetY = pos.y;

      // Apply shift constraint if shift is pressed and we have at least one point
      if (isShiftPressed && currentDrawing.points.length >= 2) {
        const lastPointIndex = currentDrawing.points.length - 2;
        const lastX = currentDrawing.points[lastPointIndex];
        const lastY = currentDrawing.points[lastPointIndex + 1];

        const snappedPoint = snapPointTo90Degrees(lastX, lastY, pos.x, pos.y);
        targetX = snappedPoint.x;
        targetY = snappedPoint.y;
      }

      // Create preview showing straight line segments to target position (snapped or original)
      const completePreviewPoints = [
        ...currentDrawing.points,
        targetX,
        targetY,
      ];

      // For surface components with 2+ points, add closing line back to start
      if (currentDrawing.closed && currentDrawing.points.length >= 4) {
        completePreviewPoints.push(
          currentDrawing.points[0],
          currentDrawing.points[1],
        );
      }

      setCurrentDrawing({
        ...currentDrawing,
        previewPoints: completePreviewPoints,
      });
    }

    if (currentDrawing.type === 'arrow' && currentDrawing.isDrawing) {
      // Create preview showing arrow path to current mouse position
      const completePreviewPoints = [...currentDrawing.points, pos.x, pos.y];

      setCurrentDrawing({
        ...currentDrawing,
        previewPoints: completePreviewPoints,
      });
    }
  };

  // Handle mouse up event for drawing
  const handleMouseUp = (e?: Konva.KonvaEventObject<MouseEvent>) => {
    // Handle middle mouse button release
    if (e && e.evt.button === 1) {
      setIsMiddleMousePanning(false);
      setIsMiddleMouseDragging(false);
      setMiddleMouseStartPos(null);
      // Trigger bounce-back if panning went beyond boundaries
      panningConstraints.triggerBounceBack();
      return;
    }

    if (!currentDrawing) {
      return;
    }

    // In view mode, don't allow drawing operations
    if (!isEditMode) {
      setCurrentDrawing(null);
      return;
    }

    // Handle selection box completion
    if (selectedTool === 'select' && currentDrawing.type === 'selection') {
      // Capture the selection box before clearing it
      const selectionBox = { ...currentDrawing };

      // Clear the selection box immediately
      setCurrentDrawing(null);

      // Check if selection box is large enough (minimum 5x5 pixels)
      const minSize = 5;
      if (
        Math.abs(selectionBox.width) < minSize ||
        Math.abs(selectionBox.height) < minSize
      ) {
        // Clear selection on small drag (essentially a click)
        setSelectedDrawingIds([]);
        return;
      }

      // Use setTimeout to avoid blocking the main thread
      setTimeout(() => {
        // Find drawings inside the selection box
        const selectedIds: number[] = [];

        fetchedDrawings.forEach((drawing) => {
          const config = drawing.config;
          const drawingData = {
            x: parseFloat(config.x || '0'),
            y: parseFloat(config.y || '0'),
            width: config.width ? parseFloat(config.width) : undefined,
            height: config.height ? parseFloat(config.height) : undefined,
            radius: config.radius ? parseFloat(config.radius) : undefined,
            radiusX: config.radiusX ? parseFloat(config.radiusX) : undefined,
            radiusY: config.radiusY ? parseFloat(config.radiusY) : undefined,
            points: config.points ? JSON.parse(config.points) : undefined,
          };

          if (isDrawingInSelectionBox(drawingData, selectionBox)) {
            selectedIds.push(drawing.id);
          }
        });

        // Update selected drawings based on shift key state
        if (shiftPressedDuringDrag) {
          // Add to existing selection (merge with current selection)
          const combinedSelection = [
            ...new Set([...selectedDrawingIds, ...selectedIds]),
          ];
          setSelectedDrawingIds(combinedSelection);
        } else {
          // Replace selection
          setSelectedDrawingIds(selectedIds);
        }

        // Reset shift tracking
        setShiftPressedDuringDrag(false);
      }, 0);

      return;
    }

    if (selectedTool === 'pan') {
      return;
    }

    // Handle curve tool clicks
    if (
      selectedTool === 'curve' &&
      currentDrawing.type === 'curve' &&
      currentDrawing.isDrawing
    ) {
      // If spacebar is pressed or middle mouse button is held (panning mode), don't interpret clicks as drawing actions for the curve.
      // The stage drag (panning) should take precedence.
      if (isSpacebarPanning || isMiddleMousePanning) {
        // It's important that the stage is draggable when panning is active.
        // This return prevents adding points to the curve during an intended pan.
        return;
      }
      // Check for double-click to finish curve
      const now = Date.now();
      const timeSinceLastClick = now - (lastClickTime || 0);
      setLastClickTime(now);

      if (timeSinceLastClick < 300) {
        // Double-click detected
        // Finish the curve - need at least 2 points to create a valid curve
        if (currentDrawing.points.length < 4) {
          toast.error('Curve needs at least 2 anchor points');
          setCurrentDrawing(null);
          return;
        }

        // Check if drawing is within bounds
        if (!isDrawingWithinBounds(currentDrawing, dimensions)) {
          toast.error("Drawing can't go out of plan extent");
          setCurrentDrawing(null);
          return;
        }

        // Check for self-intersection in curve surface drawings
        if (currentDrawing.type === 'curve') {
          let isSurface = selectedComponentItem?.geometryType === 'surface';

          // If in measure mode, use measure tool properties
          if (isMeasureMode) {
            const measureProps = getMeasureToolProperties(selectedMeasureTool);
            isSurface = measureProps.isSurface;
          }

          if (
            isSurface &&
            currentDrawing.closed &&
            hasSelfintersection(currentDrawing.points, currentDrawing.closed)
          ) {
            toast.error('Surface areas cannot have self-intersecting lines');
            setCurrentDrawing(null);
            return;
          }
        }

        // Save to API
        if (currentBlueprintImageId && currentDrawing) {
          const config: Record<string, string> = {
            type: currentDrawing.type,
            x: String(currentDrawing.x),
            y: String(currentDrawing.y),
            fill: currentDrawing.fill,
            stroke: currentDrawing.stroke,
            strokeWidth: String(currentDrawing.strokeWidth),
            points: JSON.stringify(currentDrawing.points),
            tension: String(currentDrawing.tension),
            closed: String(currentDrawing.closed),
          };

          const payload: CreateDrawingPayload = {
            blueprintImageId: currentBlueprintImageId,
            config,
          };

          // In measure mode, don't assign componentId
          if (!isMeasureMode && selectedComponentItem?.id) {
            payload.componentId = selectedComponentItem.id;
            payload._optimisticComponent = {
              id: selectedComponentItem.id,
              name: selectedComponentItem.name,
              color: selectedComponentItem.color,
              shade: selectedComponentItem.shade,
            };
          }

          createDrawingMutation.mutate(payload, {
            onSuccess: (response) => {
              recordAction({
                type: 'create',
                data: {
                  drawings: [response.data],
                },
              });
            },
          });
        }

        setCurrentDrawing(null);
        return;
      }

      // Single click - add anchor point and continue drawing
      if (!e) return; // Need event to get stage
      const stage = e.target.getStage();
      if (!stage) return;

      const pos = getRelativePointerPosition(stage, position, zoom);
      if (!pos) return;

      setCurrentDrawing({
        ...currentDrawing,
        points: [...currentDrawing.points, pos.x, pos.y],
        previewPoints: [], // Clear preview, will be updated on next mouse move
      });

      return; // Don't finish drawing yet
    }

    // Handle point-to-point tool clicks
    if (
      selectedTool === 'point-to-point' &&
      currentDrawing.type === 'point-to-point' &&
      currentDrawing.isDrawing
    ) {
      if (isSpacebarPanning || isMiddleMousePanning) {
        return;
      }

      // Check for double-click to finish
      const now = Date.now();
      const timeSinceLastClick = now - (lastClickTime || 0);
      setLastClickTime(now);

      if (timeSinceLastClick < 300) {
        // Double-click detected
        // Remove duplicate consecutive points for validation
        const getUniquePoints = (points: number[]): number[] => {
          const uniquePoints: number[] = [];
          for (let i = 0; i < points.length; i += 2) {
            const x = points[i];
            const y = points[i + 1];

            // Check if this point is different from the last added point
            if (
              uniquePoints.length === 0 ||
              uniquePoints[uniquePoints.length - 2] !== x ||
              uniquePoints[uniquePoints.length - 1] !== y
            ) {
              uniquePoints.push(x, y);
            }
          }
          return uniquePoints;
        };

        const uniquePoints = getUniquePoints(currentDrawing.points);
        // For surface components, need at least 3 points (6 coordinates)
        const minPoints = currentDrawing.closed ? 6 : 4;
        const minPointsText = currentDrawing.closed
          ? '3 anchor points'
          : '2 anchor points';

        if (uniquePoints.length < minPoints) {
          toast.error(
            `Point-to-point ${
              currentDrawing.closed ? 'surface' : 'line'
            } needs at least ${minPointsText}`,
          );
          setCurrentDrawing(null);
          return;
        }

        // Check bounds and save
        if (!isDrawingWithinBounds(currentDrawing, dimensions)) {
          toast.error("Drawing can't go out of plan extent");
          setCurrentDrawing(null);
          return;
        }

        // Check for self-intersection in point-to-point surface drawings
        if (currentDrawing.type === 'point-to-point') {
          let isSurface = selectedComponentItem?.geometryType === 'surface';

          // If in measure mode, use measure tool properties
          if (isMeasureMode) {
            const measureProps = getMeasureToolProperties(selectedMeasureTool);
            isSurface = measureProps.isSurface;
          }

          if (
            isSurface &&
            currentDrawing.closed &&
            hasSelfintersection(currentDrawing.points, currentDrawing.closed)
          ) {
            toast.error('Surface areas cannot have self-intersecting lines');
            setCurrentDrawing(null);
            return;
          }
        }

        // Save to API
        if (currentBlueprintImageId && currentDrawing) {
          const config: Record<string, string> = {
            type: currentDrawing.type,
            x: String(currentDrawing.x),
            y: String(currentDrawing.y),
            fill: currentDrawing.fill,
            stroke: currentDrawing.stroke,
            strokeWidth: String(currentDrawing.strokeWidth),
            points: JSON.stringify(currentDrawing.points),
            closed: String(currentDrawing.closed),
          };

          const payload: CreateDrawingPayload = {
            blueprintImageId: currentBlueprintImageId,
            config,
          };

          // In measure mode, don't assign componentId
          if (!isMeasureMode && selectedComponentItem?.id) {
            payload.componentId = selectedComponentItem.id;
            payload._optimisticComponent = {
              id: selectedComponentItem.id,
              name: selectedComponentItem.name,
              color: selectedComponentItem.color,
              shade: selectedComponentItem.shade,
            };
          }

          createDrawingMutation.mutate(payload, {
            onSuccess: (response) => {
              recordAction({
                type: 'create',
                data: {
                  drawings: [response.data],
                },
              });
            },
          });
        }

        setCurrentDrawing(null);
        return;
      }

      // Single click - add anchor point and continue drawing
      // For surface components, ignore double-click if we only have 1 point
      if (currentDrawing.closed && currentDrawing.points.length === 2) {
        // Force single-click behavior for surface with only 1 point
      }

      if (!e) return;
      const stage = e.target.getStage();
      if (!stage) return;

      const pos = getRelativePointerPosition(stage, position, zoom);
      if (!pos) return;

      // Determine target position - apply shift constraint if shift is pressed and we have at least one point
      let targetX = pos.x;
      let targetY = pos.y;

      // Apply shift constraint if shift is pressed and we have at least one point
      if (isShiftPressed && currentDrawing.points.length >= 2) {
        const lastPointIndex = currentDrawing.points.length - 2;
        const lastX = currentDrawing.points[lastPointIndex];
        const lastY = currentDrawing.points[lastPointIndex + 1];

        const snappedPoint = snapPointTo90Degrees(lastX, lastY, pos.x, pos.y);
        targetX = snappedPoint.x;
        targetY = snappedPoint.y;
      }

      setCurrentDrawing({
        ...currentDrawing,
        points: [...currentDrawing.points, targetX, targetY],
        previewPoints: [], // Clear preview, will be updated on next mouse move
      });

      return; // Don't finish drawing yet
    }

    // Handle arrow tool clicks
    if (
      selectedTool === 'arrow' &&
      currentDrawing.type === 'arrow' &&
      currentDrawing.isDrawing
    ) {
      if (isSpacebarPanning || isMiddleMousePanning) {
        return;
      }

      // Check for double-click to finish
      const now = Date.now();
      const timeSinceLastClick = now - (lastClickTime || 0);
      setLastClickTime(now);

      if (timeSinceLastClick < 300) {
        // Double-click detected - finish arrow
        if (currentDrawing.points.length < 4) {
          toast.error('Arrow needs at least 2 anchor points');
          setCurrentDrawing(null);
          return;
        }

        // Check bounds and save
        if (!isDrawingWithinBounds(currentDrawing, dimensions)) {
          toast.error("Drawing can't go out of plan extent");
          setCurrentDrawing(null);
          return;
        }

        // Save to API
        if (currentBlueprintImageId && currentDrawing) {
          const config: Record<string, string> = {
            type: currentDrawing.type,
            x: String(currentDrawing.x),
            y: String(currentDrawing.y),
            fill: currentDrawing.fill,
            stroke: currentDrawing.stroke,
            strokeWidth: String(currentDrawing.strokeWidth),
            points: JSON.stringify(currentDrawing.points),
            pointerLength: String(currentDrawing.pointerLength),
            pointerWidth: String(currentDrawing.pointerWidth),
          };

          const payload: CreateDrawingPayload = {
            blueprintImageId: currentBlueprintImageId,
            config,
          };

          // Arrow tool is only available in measure mode (no component selected)
          // Don't assign componentId for arrows

          createDrawingMutation.mutate(payload, {
            onSuccess: (response) => {
              recordAction({
                type: 'create',
                data: {
                  drawings: [response.data],
                },
              });
            },
          });
        }

        setCurrentDrawing(null);
        return;
      }

      // Single click - add anchor point and continue drawing
      if (!e) return; // Need event to get stage
      const stage = e.target.getStage();
      if (!stage) return;

      const pos = getRelativePointerPosition(stage, position, zoom);
      if (!pos) return;

      setCurrentDrawing({
        ...currentDrawing,
        points: [...currentDrawing.points, pos.x, pos.y],
        previewPoints: [], // Clear preview, will be updated on next mouse move
      });

      return; // Don't finish drawing yet
    }

    // Prevent adding zero-size shapes (curve and point-to-point are handled separately above)
    if (
      (currentDrawing.type === 'rectangle' &&
        (currentDrawing.width === 0 || currentDrawing.height === 0)) ||
      (currentDrawing.type === 'circle' && currentDrawing.radius === 0) ||
      (currentDrawing.type === 'ellipse' &&
        (currentDrawing.radiusX === 0 || currentDrawing.radiusY === 0)) ||
      (currentDrawing.type === 'freehand' && currentDrawing.points.length < 4) // Need at least 2 points (4 coordinates)
    ) {
      setCurrentDrawing(null); // Clear current drawing without adding
      return;
    }

    // Check if drawing is within ImageLayer bounds
    if (!isDrawingWithinBounds(currentDrawing, dimensions)) {
      toast.error("Drawing can't go out of plan extent");
      setCurrentDrawing(null); // Clear current drawing without adding
      return;
    }

    // Check for self-intersection in freehand surface drawings
    if (currentDrawing.type === 'freehand') {
      let isSurface = selectedComponentItem?.geometryType === 'surface';

      // If in measure mode, use measure tool properties
      if (isMeasureMode) {
        const measureProps = getMeasureToolProperties(selectedMeasureTool);
        isSurface = measureProps.isSurface;
      }

      if (
        isSurface &&
        hasSelfintersection(currentDrawing.points, currentDrawing.closed)
      ) {
        toast.error('Surface areas cannot have self-intersecting lines');
        setCurrentDrawing(null); // Clear current drawing without adding
        return;
      }
    }

    // API Integration: Create Drawing
    if (currentBlueprintImageId && currentDrawing) {
      const config: Record<string, string> = {
        type: currentDrawing.type,
        x: String(currentDrawing.x),
        y: String(currentDrawing.y),
        fill: currentDrawing.fill,
        stroke: currentDrawing.stroke,
        strokeWidth: String(currentDrawing.strokeWidth),
      };

      if (currentDrawing.type === 'rectangle') {
        config.width = String(currentDrawing.width);
        config.height = String(currentDrawing.height);
      } else if (currentDrawing.type === 'circle') {
        config.radius = String(currentDrawing.radius);
      } else if (currentDrawing.type === 'ellipse') {
        config.radiusX = String(currentDrawing.radiusX);
        config.radiusY = String(currentDrawing.radiusY);
      } else if (currentDrawing.type === 'freehand') {
        config.points = JSON.stringify(currentDrawing.points);
        config.closed = String(currentDrawing.closed);
        config.tension = String(currentDrawing.tension);
        if (currentDrawing.thickness !== undefined) {
          config.thickness = String(currentDrawing.thickness);
        }
      } else if (currentDrawing.type === 'curve') {
        config.points = JSON.stringify(currentDrawing.points);
        config.tension = String(currentDrawing.tension);
        config.closed = String(currentDrawing.closed);
        if (currentDrawing.thickness !== undefined) {
          config.thickness = String(currentDrawing.thickness);
        }
      } else if (currentDrawing.type === 'point-to-point') {
        config.points = JSON.stringify(currentDrawing.points);
        config.closed = String(currentDrawing.closed);
        if (currentDrawing.thickness !== undefined) {
          config.thickness = String(currentDrawing.thickness);
        }
      } else if (currentDrawing.type === 'arrow') {
        config.points = JSON.stringify(currentDrawing.points);
        config.pointerLength = String(currentDrawing.pointerLength);
        config.pointerWidth = String(currentDrawing.pointerWidth);
      } else if (currentDrawing.type === 'point') {
        config.points = JSON.stringify(currentDrawing.points);
        config.closed = String(currentDrawing.closed);
        config.pointType = currentDrawing.pointType;
        config.radius = String(currentDrawing.radius || DEFAULT_POINT_RADIUS);
      }

      const payload: CreateDrawingPayload = {
        blueprintImageId: currentBlueprintImageId,
        config,
      };

      // In measure mode, don't assign componentId
      if (!isMeasureMode && selectedComponentItem?.id) {
        payload.componentId = selectedComponentItem.id;
        // Include component data for optimistic update to show correct colors immediately
        payload._optimisticComponent = {
          id: selectedComponentItem.id,
          name: selectedComponentItem.name,
          color: selectedComponentItem.color,
          shade: selectedComponentItem.shade,
        };
      }

      createDrawingMutation.mutate(payload, {
        onSuccess: (response) => {
          // Record the create action for undo/redo
          recordAction({
            type: 'create',
            data: {
              drawings: [response.data],
            },
          });
        },
      });
    } else {
      console.warn('Cannot save drawing: blueprintImageId is missing.');
    }

    setCurrentDrawing(null); // Clear current drawing after attempting to save
  };

  // Handle drag end event for existing shapes
  const handleDragEnd = (
    drawingId: number,
    event: Konva.KonvaEventObject<DragEvent>,
  ) => {
    if (!currentBlueprintImageId) {
      console.error(
        'Cannot update drawing: currentBlueprintImageId is missing.',
      );
      return;
    }

    // In view mode, don't allow dragging/editing drawings
    if (!isEditMode) {
      setIsShapeDragging(false);
      return;
    }

    const targetNode = event.target;

    // Get the drawing from the query cache
    const drawings = queryClient.getQueryData<Drawing[]>(
      queryKeys.takeoff.drawings(currentBlueprintImageId),
    );

    const originalDrawing = drawings?.find((d: Drawing) => d.id === drawingId);

    if (!originalDrawing) {
      console.error('Original drawing not found for drag end:', drawingId);
      return;
    }

    // Create a drawing object to check bounds
    const draggedDrawing = {
      x: targetNode.x(),
      y: targetNode.y(),
      width: originalDrawing.config.width
        ? parseFloat(originalDrawing.config.width)
        : undefined,
      height: originalDrawing.config.height
        ? parseFloat(originalDrawing.config.height)
        : undefined,
      radius: originalDrawing.config.radius
        ? parseFloat(originalDrawing.config.radius)
        : undefined,
      radiusX: originalDrawing.config.radiusX
        ? parseFloat(originalDrawing.config.radiusX)
        : undefined,
      radiusY: originalDrawing.config.radiusY
        ? parseFloat(originalDrawing.config.radiusY)
        : undefined,
      points: originalDrawing.config.points
        ? (() => {
            // For freehand drawings, calculate new points based on drag offset
            const originalPoints = JSON.parse(originalDrawing.config.points);
            const originalX = parseFloat(originalDrawing.config.x || '0');
            const originalY = parseFloat(originalDrawing.config.y || '0');
            const offsetX = targetNode.x() - originalX;
            const offsetY = targetNode.y() - originalY;

            const newPoints = [];
            for (let i = 0; i < originalPoints.length; i += 2) {
              newPoints.push(originalPoints[i] + offsetX);
              newPoints.push(originalPoints[i + 1] + offsetY);
            }
            return newPoints;
          })()
        : undefined,
    };

    // Check if dragged drawing is within ImageLayer bounds
    if (!isDrawingWithinBounds(draggedDrawing, dimensions)) {
      toast.error("Drawing can't go out of plan extent");
      // Reset the drawing position to its original location
      targetNode.x(parseFloat(originalDrawing.config.x || '0'));
      targetNode.y(parseFloat(originalDrawing.config.y || '0'));
      setIsShapeDragging(false);
      return;
    }

    const payload: UpdateDrawingPayload = {
      drawingId: drawingId,
      blueprintImageId: currentBlueprintImageId,
      config: {
        ...originalDrawing.config, // Preserve existing config
        x: String(targetNode.x()),
        y: String(targetNode.y()),
      },
    };

    // For freehand drawings, update the points with the new positions
    if (draggedDrawing.points) {
      payload.config.points = JSON.stringify(draggedDrawing.points);
    }

    // Handle componentId: API expects number, Drawing type has number | null
    if (
      originalDrawing.componentId !== null &&
      originalDrawing.componentId !== undefined
    ) {
      payload.componentId = originalDrawing.componentId;
    }

    updateDrawingMutation.mutate(payload, {
      onSuccess: () => {
        // Record the move action for undo/redo
        recordAction({
          type: 'move',
          data: {
            drawings: [
              {
                id: drawingId,
                oldData: {
                  config: originalDrawing.config,
                  componentId: originalDrawing.componentId,
                },
                newData: {
                  config: payload.config,
                  componentId: payload.componentId ?? null,
                },
              },
            ],
          },
        });
      },
      onSettled: () => {
        setIsShapeDragging(false); // Ensure stage draggability is restored
      },
    });
  };

  // Handle drawing selection
  const handleDrawingSelect = (drawingId: number) => {
    // In view mode, don't allow selection of drawings
    if (!isEditMode) {
      return;
    }

    if (selectedTool === 'select') {
      if (isShiftPressed) {
        // Multi-selection mode: build new selection array based on current state
        let newSelection = [...selectedDrawingIds];

        // If we have a single selection that's not in multi-selection, add it first
        if (
          selectedDrawingId !== null &&
          !newSelection.includes(selectedDrawingId)
        ) {
          newSelection.push(selectedDrawingId);
        }

        // Toggle the clicked drawing
        const isCurrentlySelected =
          newSelection.includes(drawingId) || selectedDrawingId === drawingId;

        if (isCurrentlySelected) {
          // Remove from selection
          newSelection = newSelection.filter((id) => id !== drawingId);
        } else {
          // Add to selection
          newSelection.push(drawingId);
        }

        // Update the selection state
        setSelectedDrawingIds(newSelection);
        setSelectedDrawingId(null); // Clear single selection when using multi-select
      } else {
        // Single selection mode
        setSelectedDrawingId(drawingId);
        clearSelection();
      }
    }
  };

  // Comment handlers
  const handleCommentSave = useCallback(
    (text: string) => {
      if (!currentBlueprintImageId || !isEditMode) {
        setShowCommentForm(false);
        return;
      }

      const config = {
        type: 'comment',
        x: commentCanvasPosition.x.toString(),
        y: commentCanvasPosition.y.toString(),
        text,
        width: '200',
      };

      const createPayload: CreateDrawingPayload = {
        blueprintImageId: currentBlueprintImageId,
        // Comments don't belong to any component, so omit componentId
        config,
      };

      createDrawingMutation.mutate(createPayload, {
        onSuccess: (response) => {
          // Record action for undo/redo
          recordAction({
            type: 'create',
            data: {
              drawings: [response.data],
            },
          });

          // Invalidate queries to refresh the drawing list
          queryClient.invalidateQueries({
            queryKey: queryKeys.takeoff.drawings(currentBlueprintImageId),
          });

          toast.success('Comment added successfully');
        },
        onError: (error) => {
          console.error('Error creating comment:', error);
          toast.error('Failed to add comment');
        },
      });

      setShowCommentForm(false);
    },
    [
      currentBlueprintImageId,
      isEditMode,
      commentCanvasPosition,
      createDrawingMutation,
      recordAction,
      queryClient,
    ],
  );

  const handleCommentCancel = useCallback(() => {
    setShowCommentForm(false);
  }, []);

  // Handle stage click for deselection and comment tool
  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    // Only handle left mouse button clicks (button 0), ignore middle/right clicks
    if (e.evt.button !== 0) {
      return;
    }

    // Check if clicked on empty area (stage itself) or on the image layer
    const clickedOnStage = e.target === e.target.getStage();
    const clickedOnImage = e.target.getClassName() === 'Image';

    // Handle comment tool clicks (but not when panning)
    // Comments can be placed anywhere on the canvas, including on top of other drawings
    if (
      selectedTool === 'comment' &&
      isEditMode &&
      !isSpacebarPanning &&
      !isMiddleMousePanning
    ) {
      const stage = e.target.getStage();
      if (!stage) return;

      const relativePointer = getRelativePointerPosition(stage, position, zoom);
      if (!relativePointer) return;

      // Check if click is within canvas bounds
      if (
        relativePointer.x >= 0 &&
        relativePointer.x <= dimensions.width &&
        relativePointer.y >= 0 &&
        relativePointer.y <= dimensions.height
      ) {
        // Convert canvas coordinates to screen coordinates for form positioning
        const canvasElement = stage.container();
        const rect = canvasElement.getBoundingClientRect();
        const screenX = rect.left + (relativePointer.x + position.x) * zoom;
        const screenY = rect.top + (relativePointer.y + position.y) * zoom;

        // Store both screen and canvas positions
        setCommentFormPosition({ x: screenX, y: screenY });
        setCommentCanvasPosition({
          x: relativePointer.x,
          y: relativePointer.y,
        });
        setShowCommentForm(true);
      }
      return;
    }

    // Don't deselect if shift is pressed (to allow multi-selection workflows)
    if (isShiftPressed) {
      return;
    }

    if ((clickedOnStage || clickedOnImage) && selectedTool === 'select') {
      setSelectedDrawingId(null);
      clearSelection(); // Also clear multi-selection
    }
  };

  // Handle group drag start
  const handleGroupDragStart = () => {
    setIsGroupDragging(true);
    setGroupDragOffset(null);
  };

  // Handle group drag move - provide real-time visual feedback during dragging
  const handleGroupDragMove = (deltaX: number, deltaY: number) => {
    // Update the group drag offset for real-time visual feedback
    setGroupDragOffset({ x: deltaX, y: deltaY });
  };

  // Handle group drag end - move all selected drawings by the delta
  const handleGroupDragEnd = (deltaX: number, deltaY: number) => {
    if (!currentBlueprintImageId || !isEditMode) {
      // Clear the drag offset and state
      setGroupDragOffset(null);
      setIsGroupDragging(false);
      return;
    }

    // Get selected drawings
    const selectedDrawings = fetchedDrawings.filter((d) =>
      selectedDrawingIds.includes(d.id),
    );

    if (selectedDrawings.length === 0) {
      setGroupDragOffset(null);
      setIsGroupDragging(false);
      return;
    }

    // Optimistically update the cache to prevent flicker
    const queryKey = queryKeys.takeoff.drawings(currentBlueprintImageId);

    // Optimistically update drawings in cache
    queryClient.setQueryData(queryKey, (oldData: any) => {
      if (!oldData) return oldData;

      return oldData.map((drawing: Drawing) => {
        if (selectedDrawingIds.includes(drawing.id)) {
          const config = drawing.config;
          const newX = parseFloat(config.x || '0') + deltaX;
          const newY = parseFloat(config.y || '0') + deltaY;

          const updatedConfig: any = {
            ...config,
            x: String(newX),
            y: String(newY),
          };

          // Handle drawings with points
          if (config.points) {
            const originalPoints = JSON.parse(config.points);
            const newPoints = [];
            for (let i = 0; i < originalPoints.length; i += 2) {
              newPoints.push(originalPoints[i] + deltaX);
              newPoints.push(originalPoints[i + 1] + deltaY);
            }
            updatedConfig.points = JSON.stringify(newPoints);
          }

          return {
            ...drawing,
            config: updatedConfig,
          };
        }
        return drawing;
      });
    });

    // Clear the drag offset immediately since cache is now updated
    setGroupDragOffset(null);

    // Check if the entire group will be within bounds after the move
    const groupBoundsValid = selectedDrawings.every((drawing) => {
      const config = drawing.config;
      const newX = parseFloat(config.x || '0') + deltaX;
      const newY = parseFloat(config.y || '0') + deltaY;

      const drawingWithNewPos = {
        x: newX,
        y: newY,
        width: config.width ? parseFloat(config.width) : undefined,
        height: config.height ? parseFloat(config.height) : undefined,
        radius: config.radius ? parseFloat(config.radius) : undefined,
        radiusX: config.radiusX ? parseFloat(config.radiusX) : undefined,
        radiusY: config.radiusY ? parseFloat(config.radiusY) : undefined,
        points: config.points
          ? (() => {
              const originalPoints = JSON.parse(config.points);
              const newPoints = [];
              for (let i = 0; i < originalPoints.length; i += 2) {
                newPoints.push(originalPoints[i] + deltaX);
                newPoints.push(originalPoints[i + 1] + deltaY);
              }
              return newPoints;
            })()
          : undefined,
      };

      return isDrawingWithinBounds(drawingWithNewPos, dimensions);
    });

    if (!groupBoundsValid) {
      toast.error("Group can't be moved out of plan extent");
      setIsGroupDragging(false);
      return;
    }

    // Create update payloads for all selected drawings
    const updatePromises = selectedDrawings.map((drawing) => {
      const config = drawing.config;
      const newX = parseFloat(config.x || '0') + deltaX;
      const newY = parseFloat(config.y || '0') + deltaY;

      const payload: UpdateDrawingPayload = {
        drawingId: drawing.id,
        blueprintImageId: currentBlueprintImageId,
        config: {
          ...config,
          x: String(newX),
          y: String(newY),
        },
      };

      // For drawings with points (freehand, curves, etc.), update the points
      if (config.points) {
        const originalPoints = JSON.parse(config.points);
        const newPoints = [];
        for (let i = 0; i < originalPoints.length; i += 2) {
          newPoints.push(originalPoints[i] + deltaX);
          newPoints.push(originalPoints[i + 1] + deltaY);
        }
        payload.config.points = JSON.stringify(newPoints);
      }

      // Handle componentId
      if (drawing.componentId !== null && drawing.componentId !== undefined) {
        payload.componentId = drawing.componentId;
      }

      return updateDrawingMutation.mutateAsync(payload);
    });

    // Execute all updates
    Promise.all(updatePromises)
      .then(() => {
        // Optimistic update was successful, record for undo/redo
        recordAction({
          type: 'move',
          data: {
            drawings: selectedDrawings.map((drawing) => ({
              id: drawing.id,
              oldData: {
                config: drawing.config,
                componentId: drawing.componentId,
              },
              newData: {
                config: {
                  ...drawing.config,
                  x: String(parseFloat(drawing.config.x || '0') + deltaX),
                  y: String(parseFloat(drawing.config.y || '0') + deltaY),
                  ...(drawing.config.points && {
                    points: JSON.stringify(
                      (() => {
                        const originalPoints = JSON.parse(
                          drawing.config.points,
                        );
                        const newPoints = [];
                        for (let i = 0; i < originalPoints.length; i += 2) {
                          newPoints.push(originalPoints[i] + deltaX);
                          newPoints.push(originalPoints[i + 1] + deltaY);
                        }
                        return newPoints;
                      })(),
                    ),
                  }),
                },
                componentId: drawing.componentId,
              },
            })),
          },
        });
      })
      .catch((error) => {
        console.error('Failed to update group:', error);
        toast.error('Failed to move group');
        // On error, rollback the optimistic update by refetching
        queryClient.invalidateQueries({
          queryKey: queryKeys.takeoff.drawings(currentBlueprintImageId),
        });
      })
      .finally(() => {
        setIsGroupDragging(false);
      });
  };

  // Handle canceling current drawing operation
  const cancelCurrentDrawing = () => {
    if (currentDrawing) {
      setCurrentDrawing(null);
    }
  };

  return {
    selectedTool,
    setSelectedTool,
    currentDrawing,
    isShapeDragging,
    setIsShapeDragging,
    isGroupDragging,
    setIsGroupDragging,
    groupDragOffset,
    isSpacebarPanning,
    setIsSpacebarPanning,
    isMiddleMousePanning,
    setIsMiddleMousePanning,
    showCommentForm,
    commentFormPosition,
    handleCommentSave,
    handleCommentCancel,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleDragEnd,
    handleGroupDragStart,
    handleGroupDragMove,
    handleGroupDragEnd,
    handleDrawingSelect,
    handleStageClick,
    cancelCurrentDrawing,
  };
}
