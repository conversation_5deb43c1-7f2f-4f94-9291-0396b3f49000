'use client';

import React, { RefObject } from 'react';
import { ArrowUp, Square, Paperclip, X, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { AIModel, AI_MODEL_OPTIONS } from './types/chatbot-types';
import {
  FileUploadState,
  FileUploadActions,
  formatBytes,
} from '@/hooks/use-file-upload';

interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  onStop?: () => void;
  isStreaming?: boolean;
  textareaRef?: RefObject<HTMLTextAreaElement | null>;
  // File upload props
  fileUploadState: FileUploadState;
  fileUploadActions: FileUploadActions;
  maxFiles?: number;
}

// New compact file preview component based on the examples
const FilePreviewComponent: React.FC<{
  file: {
    file: File | { name: string; size: number; type: string };
    id: string;
    preview?: string;
  };
  onRemove: (id: string) => void;
}> = ({ file, onRemove }) => {
  const fileType = file.file instanceof File ? file.file.type : file.file.type;
  const fileName = file.file instanceof File ? file.file.name : file.file.name;
  const fileSize = file.file instanceof File ? file.file.size : file.file.size;
  const isImage = fileType.startsWith('image/');

  return (
    <div className="bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3">
      <div className="flex items-center gap-3 overflow-hidden">
        <div className="bg-accent aspect-square shrink-0 rounded">
          {isImage && file.preview ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={file.preview}
              alt={fileName}
              className="size-10 rounded-[inherit] object-cover"
            />
          ) : (
            <div className="flex aspect-square size-10 shrink-0 items-center justify-center rounded border">
              <FileText className="size-4 opacity-60" />
            </div>
          )}
        </div>
        <div className="flex min-w-0 flex-col gap-0.5">
          <p className="truncate text-[13px] font-medium">{fileName}</p>
          <p className="text-muted-foreground text-xs">
            {formatBytes(fileSize)}
          </p>
        </div>
      </div>

      <Button
        size="icon"
        variant="ghost"
        className="text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent"
        onClick={() => onRemove(file.id)}
        aria-label="Remove file"
      >
        <X className="size-4" aria-hidden="true" />
      </Button>
    </div>
  );
};

export const ChatbotInput: React.FC<ChatbotInputProps> = ({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
  selectedModel,
  onModelChange,
  onStop,
  isStreaming = false,
  textareaRef,
  fileUploadState,
  fileUploadActions,
  maxFiles = 2,
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (
        (input.trim() || fileUploadState.files.length > 0) &&
        !isLoading &&
        !disabled
      ) {
        handleSubmit(e);
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  const handleButtonClick = (e: React.FormEvent) => {
    if (isStreaming && onStop) {
      onStop();
    } else {
      handleSubmit(e);
    }
  };

  const isSubmitDisabled =
    (!input.trim() && fileUploadState.files.length === 0) ||
    isLoading ||
    disabled;
  const showStopButton = isStreaming && onStop;

  return (
    <div className="px-3 pb-3 bg-background relative">
      {/* Absolute positioned file previews above input */}
      {fileUploadState.files.length > 0 && (
        <div className="absolute bottom-full left-3 right-3 mb-2 z-10">
          <div className="bg-white rounded-xl p-3 border border-gray-200 shadow-lg">
            <div className="space-y-2">
              {fileUploadState.files.map((file) => (
                <FilePreviewComponent
                  key={file.id}
                  file={file}
                  onRemove={fileUploadActions.removeFile}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      <form
        onSubmit={showStopButton ? (e) => e.preventDefault() : handleSubmit}
      >
        {/* Big container matching the UI */}
        <div className="bg-white border border-gray-200 rounded-3xl p-4 shadow-sm hover:shadow-md transition-all duration-200">
          {/* Input area - further reduced height */}
          <div className="mb-3">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Ask ai anything about the drawing or attach files"
              disabled={isLoading || disabled}
              className={cn(
                'min-h-[18px] max-h-[120px] resize-none text-base border-0 p-0',
                'focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
                'placeholder:text-gray-400 bg-transparent w-full',
                'scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent',
              )}
              rows={1}
            />
          </div>

          {/* Bottom row: Model selector left, Attach button center, Submit button right */}
          <div className="flex items-center justify-between">
            {/* Model selector on the left - compact size */}
            <div className="flex-shrink-0">
              <Select value={selectedModel} onValueChange={onModelChange}>
                <SelectTrigger className="w-[110px] h-8 text-xs border-0 bg-gray-50 hover:bg-gray-100 rounded-lg shadow-none focus:ring-0 focus:ring-offset-0">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="rounded-lg border shadow-lg min-w-[130px]">
                  {AI_MODEL_OPTIONS.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-xs rounded focus:bg-primary/10"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Action buttons on the right */}
            <div className="flex items-center gap-2">
              {/* Attach Files Button */}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={fileUploadActions.openFileDialog}
                disabled={fileUploadState.files.length >= maxFiles}
                className="h-8 w-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg
                           transition-colors disabled:opacity-50 disabled:cursor-not-allowed
                           disabled:hover:bg-transparent"
                title={
                  fileUploadState.files.length >= maxFiles
                    ? 'Maximum files reached'
                    : 'Attach files'
                }
              >
                <Paperclip className="w-4 h-4" />
              </Button>

              {/* Submit button - fully round and small */}
              <Button
                type={showStopButton ? 'button' : 'submit'}
                size="icon"
                disabled={showStopButton ? false : isSubmitDisabled}
                onClick={showStopButton ? handleButtonClick : undefined}
                className={cn(
                  'h-10 w-10 rounded-full transition-all duration-200 cursor-pointer',
                  showStopButton
                    ? 'shadow-sm hover:shadow-md'
                    : isSubmitDisabled
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed hover:bg-gray-200'
                      : 'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md',
                )}
              >
                {showStopButton ? (
                  <Square className="h-4 w-4 bg-white" />
                ) : (
                  <ArrowUp className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>

          {/* File size info */}
          <div className="mt-2">
            <div className="text-xs text-gray-500">
              Maximum {maxFiles} files, 4MB each. Supports images and PDFs.
            </div>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          {...fileUploadActions.getInputProps()}
          className="sr-only"
          aria-label="Upload files"
        />
      </form>
    </div>
  );
};
