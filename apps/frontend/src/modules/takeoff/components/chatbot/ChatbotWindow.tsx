'use client';

import React, { useRef } from 'react';
import { ChatbotHeader } from './ChatbotHeader';
import { ChatbotMessages } from './ChatbotMessages';
import { ChatbotInput } from './ChatbotInput';
import { ChatbotWindowProps } from './types/chatbot-types';
import { useChatbot } from './hooks/useChatbot';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';

export const ChatbotWindow: React.FC<ChatbotWindowProps> = ({
  isOpen,
  onClose,
  takeoffId,
  projectName,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const {
    messages,
    isLoading,
    error,
    input,
    handleInputChange,
    handleSubmit,
    hasValidContext,
    selectedModel,
    setSelectedModel,
    stop,
    status,
    // File upload state and actions
    fileUploadState,
    fileUploadActions,
    maxFiles,
  } = useChatbot(takeoffId, textareaRef);

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent
        side="right"
        className="w-full sm:max-w-[400px] md:max-w-[480px] p-0 flex flex-col h-full"
        showCloseButton={false}
      >
        {/* Custom header without default close button positioning */}
        <SheetHeader className="p-0 space-y-0">
          {/* Hidden title for accessibility */}
          <SheetTitle className="sr-only">
            Chat with AI - {projectName}
          </SheetTitle>
          <ChatbotHeader projectName={projectName} onClose={onClose} />
        </SheetHeader>

        {/* Messages section with ScrollArea - calculated height to prevent layout shifts */}
        <div className="relative flex-1 h-[calc(100vh-280px)]">
          <ScrollArea className="h-full">
            <ChatbotMessages messages={messages} isLoading={isLoading} />
          </ScrollArea>
        </div>

        {/* Input section - fixed height at bottom */}
        <div className="h-[200px] flex-shrink-0 relative">
          <ChatbotInput
            input={input}
            handleInputChange={handleInputChange}
            handleSubmit={handleSubmit}
            isLoading={isLoading}
            disabled={!!error || !hasValidContext}
            selectedModel={selectedModel}
            onModelChange={setSelectedModel}
            onStop={stop}
            isStreaming={status === 'streaming'}
            textareaRef={textareaRef}
            // File upload props
            fileUploadState={fileUploadState}
            fileUploadActions={fileUploadActions}
            maxFiles={maxFiles}
          />

          {/* Error display */}
          {(error || !hasValidContext) && (
            <div className="px-4 py-2 bg-destructive/10 overflow-hidden">
              <p className="text-xs text-destructive break-words break-all overflow-wrap-anywhere">
                {error?.message ||
                  (typeof error === 'string' ? error : error?.toString()) ||
                  'Please select a blueprint image first'}
              </p>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};
