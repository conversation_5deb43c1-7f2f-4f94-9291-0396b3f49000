import { useCallback, useMemo, useState, RefObject } from 'react';
import { useChat } from '@ai-sdk/react';
import { useTakeoffStore } from '../../../store/takeoff-store';
import { useGetDrawings, useGetTakeoffDetails } from '../../../api/queries';
import {
  BlueprintContext,
  DrawingsContext,
  AIModel,
} from '../types/chatbot-types';
import { responseErrorInterceptor } from '@/lib/api-client';
import { useAuthStore } from '@/store/auth-store';
import { useFileUpload, FileWithPreview } from '@/hooks/use-file-upload';

export const useChatbot = (
  takeoffId: string,
  textareaRef?: RefObject<HTMLTextAreaElement | null>,
) => {
  const { selectedImage, selectedFile, setChatbotError } = useTakeoffStore();
  const token = useAuthStore((state) => state.authToken);
  const [selectedModel, setSelectedModel] = useState<AIModel>('openai');

  // File upload using the new hook
  const MAX_FILES = 2;
  const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB

  const [fileUploadState, fileUploadActions] = useFileUpload({
    maxFiles: MAX_FILES,
    maxSize: MAX_FILE_SIZE,
    accept: 'image/*,application/pdf',
    multiple: true,
    onFilesChange: (_files) => {
      // Clear any existing errors when files change
      setChatbotError(null);
    },
  });

  // Get drawings for current image
  const { data: drawingsData } = useGetDrawings(
    { blueprintImageId: selectedImage?.id || '' },
    { enabled: !!selectedImage?.id },
  );

  // Get takeoff details for system prompt
  const { data: takeoffDetails } = useGetTakeoffDetails(takeoffId);

  // Prepare context data that will be sent with each message
  const contextData = useMemo(() => {
    if (!selectedImage || !selectedFile) return null;

    const blueprintContext: BlueprintContext = {
      imageUrl: selectedImage.path,
      fileName: selectedFile.fileName,
      takeoffId: takeoffId,
      blueprintFileId: selectedFile.id,
      blueprintImageId: selectedImage.id,
    };

    const drawingsContext: DrawingsContext = {
      drawings: drawingsData || [],
      imageUrl: selectedImage.path,
      takeoffId: takeoffId,
      blueprintImageId: selectedImage.id,
    };

    return {
      blueprintContext,
      drawingsContext,
    };
  }, [selectedImage, selectedFile, takeoffId, drawingsData]);

  // Use the full power of useChat hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    status,
    error,
    append,
    reload,
    stop,
    setMessages,
    setInput,
  } = useChat({
    api: `${process.env.NEXT_PUBLIC_BASE_URL}/chatbot/chat`,
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: contextData
      ? {
          contextData,
          model: selectedModel,
          systemPrompt: takeoffDetails?.ai_system_prompt,
        }
      : {
          model: selectedModel,
          systemPrompt: takeoffDetails?.ai_system_prompt,
        },
    onError: (error) => {
      const axiosError = {
        response: {
          status: JSON.parse(error.message)?.statusCode,
          data: JSON.parse(error.message),
        },
      };
      console.error(error);
      responseErrorInterceptor(axiosError, handleCustomSubmit);
    },
    onResponse: async (response) => {
      if (!response.ok) {
        setChatbotError('Failed to get response from AI assistant');
      } else {
        setChatbotError(null);
      }
    },
    onFinish: () => {
      setChatbotError(null);
      // Clear files after successful submission
      fileUploadActions.clearFiles();
      // Focus the input field after chat completion with proper timing
      if (textareaRef?.current) {
        // Use setTimeout to ensure DOM updates are complete
        setTimeout(() => {
          if (textareaRef.current) {
            textareaRef.current.focus();
            // Also ensure cursor is at the end if there's any text
            const length = textareaRef.current.value.length;
            textareaRef.current.setSelectionRange(length, length);
          }
        }, 0);
      }
    },
  });

  // Helper function to convert FileWithPreview to FileList for experimental_attachments
  const convertFilesToFileList = useCallback(
    (files: FileWithPreview[]): FileList | undefined => {
      if (files.length === 0) return undefined;

      const dataTransfer = new DataTransfer();
      files.forEach((fileWithPreview) => {
        if (fileWithPreview.file instanceof File) {
          dataTransfer.items.add(fileWithPreview.file);
        }
      });
      return dataTransfer.files;
    },
    [],
  );

  // Custom submit handler that includes context validation and file attachments
  const handleCustomSubmit = useCallback(
    (e?: React.FormEvent, options?: { allowEmptySubmit?: boolean }) => {
      e?.preventDefault();

      if (!selectedImage || !selectedFile) {
        setChatbotError('Please select a blueprint image first');
        return;
      }

      if (!contextData) {
        setChatbotError('Context data not available');
        return;
      }

      if (
        !input.trim() &&
        fileUploadState.files.length === 0 &&
        !options?.allowEmptySubmit
      ) {
        setChatbotError('Please enter a message or attach a file.');
        return;
      }

      // Check for file upload errors
      if (fileUploadState.errors.length > 0) {
        setChatbotError(fileUploadState.errors[0]);
        return;
      }

      setChatbotError(null);

      // Convert files to FileList for experimental_attachments
      const fileList = convertFilesToFileList(fileUploadState.files);

      // Use the standard handleSubmit with context data and experimental_attachments
      handleSubmit(e, {
        data: JSON.parse(JSON.stringify(contextData)),
        experimental_attachments: fileList,
      });
    },
    [
      selectedImage,
      selectedFile,
      contextData,
      input,
      fileUploadState.files,
      fileUploadState.errors,
      convertFilesToFileList,
      handleSubmit,
      setChatbotError,
    ],
  );

  return {
    // AI SDK managed state
    messages,
    input,
    handleInputChange,
    handleSubmit: handleCustomSubmit,
    isLoading: status === 'streaming' || status === 'submitted',
    status,
    error,

    // Additional utilities
    append,
    reload,
    stop,
    setMessages,
    setInput,

    // Model selection
    selectedModel,
    setSelectedModel,

    // Context validation
    hasValidContext: !!contextData,

    // File upload state and actions from the new hook
    fileUploadState,
    fileUploadActions,
    maxFiles: MAX_FILES,
  };
};
