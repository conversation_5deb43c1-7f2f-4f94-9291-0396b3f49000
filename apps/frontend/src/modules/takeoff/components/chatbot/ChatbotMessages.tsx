'use client';

import React, { useEffect, useRef } from 'react';
import Image from 'next/image';
import { Bot, User, FileText } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { ChatbotMessagesProps } from './types/chatbot-types';
import { AILoadingAnimation } from './AILoadingAnimation';
import { parseSimpleMarkdown } from './utils/simpleMarkdown';

export const ChatbotMessages: React.FC<ChatbotMessagesProps> = ({
  messages,
  isLoading,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  const formatTime = (date?: Date) => {
    if (!date) return '';
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <ScrollArea
      // className="flex-1 px-4 py-4 overflow-hidden"
      className="flex-1 px-4 py-0 overflow-hidden"
      ref={scrollAreaRef}
    >
      <div className="space-y-6 overflow-hidden">
        {messages.length === 0 && !isLoading && (
          <div className="text-center py-12 px-4">
            <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-primary/10 mx-auto mb-6">
              <Bot className="h-8 w-8 text-primary" />
            </div>
            <h4 className="font-semibold text-base mb-3 break-words">
              Welcome to AI Assistant
            </h4>
            <p className="text-sm text-muted-foreground max-w-[300px] mx-auto break-words leading-relaxed">
              Ask me questions about your blueprint files or the drawings you've
              created. I can help analyze architectural plans and your custom
              annotations. You can also attach images and PDFs for analysis.
            </p>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex gap-3',
              message.role === 'user' ? 'justify-end' : 'justify-start',
            )}
          >
            {message.role === 'assistant' && (
              <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-primary/10 flex-shrink-0">
                <Bot className="h-5 w-5 text-primary" />
              </div>
            )}

            <div
              className={cn(
                'rounded-2xl px-4 py-3 text-sm overflow-hidden',
                message.role === 'user'
                  ? 'max-w-[80%] bg-primary text-primary-foreground'
                  : 'max-w-[80%] bg-muted/60 text-foreground',
              )}
            >
              {/* Display message content */}
              {message.role === 'assistant' ? (
                <div className="text-sm">
                  {parseSimpleMarkdown(message.content)}
                </div>
              ) : (
                <p className="whitespace-pre-wrap break-words break-all overflow-wrap-anywhere leading-relaxed">
                  {message.content}
                </p>
              )}

              {/* Display attachments - compact preview style */}
              {message.experimental_attachments && (
                <div className="mt-3 space-y-2">
                  {message.experimental_attachments.map((attachment, index) => {
                    const isImage =
                      attachment.contentType?.startsWith('image/');

                    return (
                      <div
                        key={`${message.id}-attachment-${index}`}
                        className="bg-background flex items-center gap-3 rounded-lg border p-2"
                      >
                        <div className="bg-accent aspect-square shrink-0 rounded">
                          {isImage ? (
                            <Image
                              src={attachment.url}
                              alt={attachment.name || 'Attached image'}
                              width={40}
                              height={40}
                              className="size-10 rounded-[inherit] object-cover"
                            />
                          ) : (
                            <div className="flex aspect-square size-10 shrink-0 items-center justify-center rounded border">
                              <FileText className="size-4 opacity-60" />
                            </div>
                          )}
                        </div>
                        <div className="flex min-w-0 flex-col gap-0.5">
                          <p className="truncate text-[13px] font-medium">
                            {attachment.name ||
                              (isImage ? 'Image' : 'PDF Document')}
                          </p>
                          <p className="text-muted-foreground text-xs">
                            {isImage ? 'Image' : 'PDF'}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              <div className="flex items-center justify-between mt-2">
                <span className="text-xs opacity-60">
                  {formatTime(message.createdAt)}
                </span>
                {/* Note: UIMessage doesn't have contextType, could be added via annotations if needed */}
              </div>
            </div>

            {message.role === 'user' && (
              <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-primary/10 flex-shrink-0">
                <User className="h-5 w-5 text-primary" />
              </div>
            )}
          </div>
        ))}

        {isLoading && (
          <div className="flex gap-3 justify-start">
            <div className="flex items-center justify-center w-9 h-9 rounded-xl bg-primary/10 flex-shrink-0">
              <Bot className="h-5 w-5 text-primary" />
            </div>
            <div className="bg-muted/60 rounded-2xl px-4 py-3 text-sm max-w-[85%] overflow-hidden">
              <AILoadingAnimation />
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
    </ScrollArea>
  );
};
