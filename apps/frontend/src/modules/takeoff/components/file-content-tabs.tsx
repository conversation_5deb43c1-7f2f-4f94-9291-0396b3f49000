'use client';

import { useState, useRef } from 'react';
import { FileText, Activity, RefreshCw } from 'lucide-react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { SidebarGroupContent, SidebarMenu } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { PageItem } from './page-item';
import { DrawingLogs, DrawingLogsRef } from './drawing-logs';
import { BlueprintFileItemType as StoreBlueprintFileItemType } from '../store/takeoff-store';

// Use types from the store for consistency
type PdfFileType = StoreBlueprintFileItemType;

interface FileContentTabsProps {
  fileData: PdfFileType;
  searchQuery?: string;
}

export function FileContentTabs({
  fileData,
  searchQuery,
}: FileContentTabsProps) {
  const [activeTab, setActiveTab] = useState<'pages' | 'logs'>('pages');
  const logsRef = useRef<DrawingLogsRef>(null);

  const handleSyncLogs = () => {
    if (logsRef.current) {
      logsRef.current.refresh();
    }
  };

  return (
    <div className="space-y-3">
      {/* Tabs for "Drawings" and "Logs" */}
      <ToggleGroup
        type="single"
        defaultValue="pages"
        value={activeTab}
        onValueChange={(value) => {
          if (value) setActiveTab(value as 'pages' | 'logs');
        }}
        className="grid w-full grid-cols-2 gap-1 rounded-lg bg-muted p-1"
      >
        <ToggleGroupItem
          value="pages"
          className="flex items-center gap-2 rounded-md px-3 py-2 text-xs font-medium transition-all data-[state=on]:bg-background data-[state=on]:text-foreground data-[state=on]:shadow-sm"
        >
          <FileText className="h-3.5 w-3.5" />
          Pages
        </ToggleGroupItem>
        <ToggleGroupItem
          value="logs"
          className="flex items-center gap-2 rounded-md px-3 py-2 text-xs font-medium transition-all data-[state=on]:bg-background data-[state=on]:text-foreground data-[state=on]:shadow-sm"
        >
          <Activity className="h-3.5 w-3.5" />
          Logs
        </ToggleGroupItem>
      </ToggleGroup>

      {/* Tab Content */}
      {activeTab === 'pages' && (
        <SidebarGroupContent>
          <SidebarMenu>
            <PageItem
              key={fileData.id}
              fileData={fileData}
              searchQuery={searchQuery}
            />
          </SidebarMenu>
        </SidebarGroupContent>
      )}

      {activeTab === 'logs' && (
        <div className="px-1 space-y-3">
          {/* Sync Button */}
          <div className="flex justify-between items-center">
            <h3 className={'text-sm text-accent-foreground'}>Drawing Logs</h3>

            <Button
              variant="outline"
              size="sm"
              onClick={handleSyncLogs}
              className="h-8 px-3 text-xs"
            >
              <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
              Sync
            </Button>
          </div>

          <DrawingLogs
            ref={logsRef}
            blueprintFileId={fileData.id}
            enabled={activeTab === 'logs'}
          />
        </div>
      )}
    </div>
  );
}
