'use client';

import React, { useState } from 'react';
import { Keyboard, Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Sheet,
  SheetContent,
  Sheet<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { useTakeoffStore } from '../store/takeoff-store';

interface ShortcutItem {
  action: string;
  keys: string[];
  category: 'drawing' | 'canvas' | 'editing' | 'navigation';
  description?: string;
  os?: 'macOS' | 'Windows' | 'Linux' | 'all';
}

const shortcuts: ShortcutItem[] = [
  // Canvas/Zoom shortcuts - Windows/Linux
  { action: 'Zoom In', keys: ['Ctrl', '+'], category: 'canvas', os: 'Windows' },
  { action: 'Zoom In', keys: ['Ctrl', '+'], category: 'canvas', os: 'Linux' },
  {
    action: 'Zoom Out',
    keys: ['Ctrl', '-'],
    category: 'canvas',
    os: 'Windows',
  },
  { action: 'Zoom Out', keys: ['Ctrl', '-'], category: 'canvas', os: 'Linux' },
  {
    action: 'Reset Zoom',
    keys: ['Ctrl', '0'],
    category: 'canvas',
    os: 'Windows',
  },
  {
    action: 'Reset Zoom',
    keys: ['Ctrl', '0'],
    category: 'canvas',
    os: 'Linux',
  },
  {
    action: 'Pinch to Zoom',
    keys: ['Ctrl', 'Scroll'],
    category: 'canvas',
    os: 'Windows',
  },
  {
    action: 'Pinch to Zoom',
    keys: ['Ctrl', 'Scroll'],
    category: 'canvas',
    os: 'Linux',
  },

  // Canvas/Zoom shortcuts - macOS
  { action: 'Zoom In', keys: ['Cmd', '+'], category: 'canvas', os: 'macOS' },
  { action: 'Zoom Out', keys: ['Cmd', '-'], category: 'canvas', os: 'macOS' },
  { action: 'Reset Zoom', keys: ['Cmd', '0'], category: 'canvas', os: 'macOS' },
  {
    action: 'Pinch to Zoom',
    keys: ['Cmd', 'Scroll'],
    category: 'canvas',
    os: 'macOS',
  },

  // Canvas/Zoom shortcuts - All platforms
  {
    action: 'Horizontal Scroll',
    keys: ['Shift', 'Scroll'],
    category: 'canvas',
    os: 'all',
  },
  {
    action: 'Vertical Scroll',
    keys: ['Alt', 'Scroll'],
    category: 'canvas',
    os: 'all',
  },

  // Drawing tools - All platforms
  { action: 'Pan Tool', keys: ['P'], category: 'drawing', os: 'all' },
  { action: 'Select Tool', keys: ['S'], category: 'drawing', os: 'all' },
  { action: 'Rectangle Tool', keys: ['R'], category: 'drawing', os: 'all' },
  { action: 'Circle Tool', keys: ['C'], category: 'drawing', os: 'all' },
  { action: 'Ellipse Tool', keys: ['E'], category: 'drawing', os: 'all' },
  { action: 'Freehand Tool', keys: ['F'], category: 'drawing', os: 'all' },
  { action: 'Curve Tool', keys: ['V'], category: 'drawing', os: 'all' },
  {
    action: 'Point-to-Point Tool',
    keys: ['T'],
    category: 'drawing',
    os: 'all',
  },
  {
    action: 'Shift Snap (Point-to-Point)',
    keys: ['Hold Shift'],
    category: 'drawing',
    os: 'all',
    description: 'Constrains lines to 90-degree angles in point-to-point mode',
  },
  { action: 'Arrow Tool', keys: ['A'], category: 'drawing', os: 'all' },

  // Editing shortcuts - Windows/Linux
  { action: 'Undo', keys: ['Ctrl', 'Z'], category: 'editing', os: 'Windows' },
  { action: 'Undo', keys: ['Ctrl', 'Z'], category: 'editing', os: 'Linux' },
  {
    action: 'Redo',
    keys: ['Ctrl', 'Shift', 'Z'],
    category: 'editing',
    os: 'Windows',
  },
  {
    action: 'Redo',
    keys: ['Ctrl', 'Shift', 'Z'],
    category: 'editing',
    os: 'Linux',
  },
  {
    action: 'Redo (Alt)',
    keys: ['Ctrl', 'Y'],
    category: 'editing',
    os: 'Windows',
  },
  { action: 'Copy', keys: ['Ctrl', 'C'], category: 'editing', os: 'Windows' },
  { action: 'Copy', keys: ['Ctrl', 'C'], category: 'editing', os: 'Linux' },
  { action: 'Cut', keys: ['Ctrl', 'X'], category: 'editing', os: 'Windows' },
  { action: 'Cut', keys: ['Ctrl', 'X'], category: 'editing', os: 'Linux' },
  { action: 'Paste', keys: ['Ctrl', 'V'], category: 'editing', os: 'Windows' },
  { action: 'Paste', keys: ['Ctrl', 'V'], category: 'editing', os: 'Linux' },
  {
    action: 'Select All',
    keys: ['Ctrl', 'A'],
    category: 'editing',
    os: 'Windows',
  },
  {
    action: 'Select All',
    keys: ['Ctrl', 'A'],
    category: 'editing',
    os: 'Linux',
  },

  // Editing shortcuts - macOS
  { action: 'Undo', keys: ['Cmd', 'Z'], category: 'editing', os: 'macOS' },
  {
    action: 'Redo',
    keys: ['Cmd', 'Shift', 'Z'],
    category: 'editing',
    os: 'macOS',
  },
  { action: 'Copy', keys: ['Cmd', 'C'], category: 'editing', os: 'macOS' },
  { action: 'Cut', keys: ['Cmd', 'X'], category: 'editing', os: 'macOS' },
  { action: 'Paste', keys: ['Cmd', 'V'], category: 'editing', os: 'macOS' },
  {
    action: 'Select All',
    keys: ['Cmd', 'A'],
    category: 'editing',
    os: 'macOS',
  },

  // Editing shortcuts - All platforms
  { action: 'Delete', keys: ['Delete'], category: 'editing', os: 'all' },
  {
    action: 'Delete (Alt)',
    keys: ['Backspace'],
    category: 'editing',
    os: 'all',
  },

  // Navigation - All platforms
  {
    action: 'Temporary Pan',
    keys: ['Space'],
    category: 'navigation',
    os: 'all',
  },
  {
    action: 'Cancel Drawing',
    keys: ['Escape'],
    category: 'navigation',
    os: 'all',
  },
];

const categoryLabels = {
  canvas: 'Canvas & Zoom',
  drawing: 'Drawing Tools',
  editing: 'Editing',
  navigation: 'Navigation',
};

const categoryOrder: (keyof typeof categoryLabels)[] = [
  'canvas',
  'drawing',
  'editing',
  'navigation',
];

const getOS = () => {
  const platform = navigator.platform.toLowerCase();

  if (platform.includes('mac')) return 'macOS';
  if (platform.includes('win')) return 'Windows';
  if (platform.includes('linux')) return 'Linux';
  return 'Unknown';
};

export function KeyboardShortcutsMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { isEditMode } = useTakeoffStore();
  const userOS = getOS();

  const filteredShortcuts = shortcuts
    .filter((shortcut) => shortcut.os === 'all' || shortcut.os === userOS)
    .filter(
      (shortcut) =>
        shortcut.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shortcut.keys.some((key) =>
          key.toLowerCase().includes(searchQuery.toLowerCase()),
        ),
    );

  const groupedShortcuts = categoryOrder.reduce(
    (acc, category) => {
      acc[category] = filteredShortcuts.filter(
        (shortcut) => shortcut.category === category,
      );
      return acc;
    },
    {} as Record<string, ShortcutItem[]>,
  );

  const formatKey = (key: string) => {
    const keyMap: Record<string, string> = {
      Ctrl: 'Ctrl',
      Cmd: '⌘',
      Option: 'Option',
      Alt: 'Alt',
      Shift: 'Shift',
      Delete: '⌫',
      Backspace: '⌫',
      Escape: 'Escape',
      Space: 'Hold Spacebar',
      Scroll: 'Scroll',
    };

    return keyMap[key] || key;
  };

  const renderShortcutKeys = (keys: string[]) => (
    <div className="flex items-center gap-1.5">
      {keys.map((key, index) => (
        <React.Fragment key={index}>
          <Badge
            variant="secondary"
            className="px-2.5 py-1.5 text-xs font-mono bg-muted/80 hover:bg-muted border border-border/50 shadow-sm"
          >
            {formatKey(key)}
          </Badge>
          {index < keys.length - 1 && (
            <span className="text-muted-foreground text-xs font-medium">+</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label="Keyboard shortcuts"
        >
          <Keyboard className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent
        side="right"
        className="w-[420px] sm:max-w-[420px] flex flex-col h-full"
      >
        <SheetHeader className="pb-6 flex-shrink-0">
          <SheetTitle className="flex items-center gap-3 text-lg font-semibold">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
              <Keyboard className="h-4 w-4 text-primary" />
            </div>
            Keyboard Shortcuts
          </SheetTitle>
        </SheetHeader>

        <div className="flex flex-col space-y-6 flex-1 min-h-0 px-2">
          {/* Search */}
          <div className="relative flex-shrink-0">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search shortcuts..."
              className="pl-9 h-10 bg-background border-border w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 hover:bg-muted"
                onClick={() => setSearchQuery('')}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Edit mode notice */}
          {!isEditMode && (
            <div className="rounded-lg bg-amber-50 border border-amber-200 p-4 flex-shrink-0">
              <p className="text-sm text-amber-800 leading-relaxed">
                Some shortcuts require edit mode to be enabled.
              </p>
            </div>
          )}

          {/* Shortcuts by category */}
          <div className="flex-1 overflow-y-auto space-y-8 pr-2">
            {categoryOrder.map((category) => {
              const categoryShortcuts = groupedShortcuts[category];
              if (categoryShortcuts.length === 0) return null;

              return (
                <div key={category} className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="h-px bg-border flex-1" />
                    <h3 className="font-semibold text-xs text-muted-foreground uppercase tracking-wider px-2">
                      {categoryLabels[category]}
                    </h3>
                    <div className="h-px bg-border flex-1" />
                  </div>
                  <div className="space-y-1">
                    {categoryShortcuts.map((shortcut, index) => (
                      <div
                        key={`${category}-${index}`}
                        className="flex items-center justify-between py-3 px-4 rounded-lg hover:bg-muted/50 transition-colors border border-transparent hover:border-border/50"
                      >
                        <span className="text-sm font-medium text-foreground">
                          {shortcut.action}
                        </span>
                        {renderShortcutKeys(shortcut.keys)}
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}

            {filteredShortcuts.length === 0 && searchQuery && (
              <div className="text-center py-12 text-muted-foreground">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-muted/50 mx-auto mb-4">
                  <Keyboard className="h-8 w-8 opacity-50" />
                </div>
                <p className="text-sm font-medium mb-1">No shortcuts found</p>
                <p className="text-xs text-muted-foreground/70">
                  Try searching for "{searchQuery.slice(0, 20)}
                  {searchQuery.length > 20 ? '...' : ''}"
                </p>
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
