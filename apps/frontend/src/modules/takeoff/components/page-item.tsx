'use client';

import Image from 'next/image';
import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Pencil } from 'lucide-react';

import { useBlueprintImages } from '../api/queries';
import { useUpdateImageScaleAndDimensions } from '../api/mutations'; // Updated import
import { SidebarMenuItem } from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import {
  useTakeoffStore,
  ImageType as StoreImageType,
  BlueprintFileItemType as StoreBlueprintFileItemType,
} from '../store/takeoff-store';
import { Skeleton } from '@/components/ui/skeleton';
import { EditScaleModal } from './EditScaleModal'; // Import the new modal
import { queryKeys } from '@/lib/query-keys'; // For query invalidation
import { toast } from '@/lib/toast'; // For notifications
import { DEFAULT_PAPER_SIZE } from '@repo/component-summary';
import { useMemo } from 'react';

// Use types from the store for consistency
type PdfFileType = StoreBlueprintFileItemType;
type ImageType = StoreImageType;
type ScaleType = NonNullable<ImageType['scale']>;
type DimensionsType = NonNullable<ImageType['dimensions']>; // Added dimensions type

interface PageItemProps {
  fileData: PdfFileType; // The parent blueprint file data
  searchQuery?: string;
}

export function PageItem({ fileData, searchQuery }: PageItemProps) {
  const { data: imagesResponse, isLoading } = useBlueprintImages(fileData.id);
  const { selectedImage, setSelectedImage, isEditMode } = useTakeoffStore();

  const [isEditScaleModalOpen, setIsEditScaleModalOpen] = useState(false);
  const [currentImageForScaleEdit, setCurrentImageForScaleEdit] =
    useState<ImageType | null>(null);
  const [currentPageNumberForModal, setCurrentPageNumberForModal] =
    useState<number>(0);

  const queryClient = useQueryClient();
  // Updated to use the new mutation hook
  const { mutateAsync: updateImageScaleAndDimensions, isPending: isUpdating } =
    useUpdateImageScaleAndDimensions();
  const { setSelectedImage: setStoreSelectedImage } = useTakeoffStore();

  // Filter images based on search query
  const filteredImages = useMemo(() => {
    if (!imagesResponse?.data || !searchQuery?.trim()) {
      return imagesResponse?.data;
    }

    return imagesResponse.data.filter((image, index) => {
      const pageNumber = (index + 1).toString();
      const filename =
        image.filename?.replace(/\.[^/.]+$/, '') || 'Untitled Sheet';
      const searchLower = searchQuery.toLowerCase();

      // Create the full display text that users actually see
      const fullDisplayText = `Page ${pageNumber}: ${filename}`.toLowerCase();

      // Search in multiple ways for better UX
      return (
        pageNumber.includes(searchLower) || // "1", "2", etc.
        filename.toLowerCase().includes(searchLower) || // filename
        fullDisplayText.includes(searchLower) || // "page 1: filename"
        `page ${pageNumber}`.includes(searchLower) || // "page 1", "page 2", etc.
        // Additional flexible matching - split search terms for better matching
        searchLower.split(/\s+/).some((term) => {
          if (term.trim() === '') return false;
          return (
            pageNumber.includes(term) || // Each term matches page number
            filename.toLowerCase().includes(term) || // Each term in filename
            `page ${pageNumber}`.includes(term)
          ); // Each term matches "page X"
        })
      );
    });
  }, [imagesResponse?.data, searchQuery]);

  const handleSave = async (
    imageId: string,
    newScale: ScaleType,
    newDimensions: DimensionsType,
    newFileName: string,
  ) => {
    if (!currentImageForScaleEdit) {
      toast.error('No image selected for update.');
      return;
    }
    try {
      await updateImageScaleAndDimensions({
        imageId,
        scale: newScale,
        dimensions: newDimensions,
        fileName: newFileName,
      });
      toast.success('Image updated successfully!');

      // Optimistically update the store
      const updatedImage = {
        ...currentImageForScaleEdit,
        scale: newScale,
        dimensions: newDimensions,
        filename: newFileName,
      };
      setStoreSelectedImage(updatedImage as ImageType); // Update the store

      await queryClient.invalidateQueries({
        queryKey: queryKeys.takeoff.blueprintImages(fileData.id),
      });

      setIsEditScaleModalOpen(false);
      setCurrentImageForScaleEdit(null);
    } catch (error) {
      console.error('Failed to update image:', error);
      toast.error('Failed to update. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <SidebarMenuItem className="py-0">
        <div className="space-y-2">
          {[1, 2].map((i) => (
            <div
              key={i}
              className="relative rounded-xl overflow-hidden shadow-sm"
            >
              <div className="relative w-full" style={{ aspectRatio: '5/3' }}>
                {/* Image skeleton */}
                <Skeleton className="w-full h-full" />

                {/* Light overlay skeletons */}
                <div className="absolute top-2 left-2 right-12">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg border border-border/50 shadow-sm">
                    <Skeleton className="h-8 w-full rounded-lg" />
                  </div>
                </div>
                <div className="absolute top-2 right-2">
                  <Skeleton className="h-7 w-7 rounded-lg shadow-md" />
                </div>
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg border border-border/50 shadow-sm">
                    <Skeleton className="h-12 w-full rounded-lg" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </SidebarMenuItem>
    );
  }

  return (
    <SidebarMenuItem className="py-0">
      <div className="space-y-2">
        {filteredImages?.map((image, index) => {
          // Calculate the original index for proper page numbering
          const originalIndex =
            imagesResponse?.data?.findIndex((img) => img.id === image.id) ??
            index;

          return (
            <div
              key={image.id}
              className={`relative group cursor-pointer rounded-xl overflow-hidden transition-all duration-200 shadow-sm ${
                selectedImage?.id === image.id
                  ? 'ring-2 ring-primary shadow-lg'
                  : 'hover:shadow-md'
              }`}
              onClick={() => setSelectedImage(image as ImageType)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ')
                  setSelectedImage(image as ImageType);
              }}
            >
              {/* Image as Card Background */}
              <div className="relative w-full" style={{ aspectRatio: '5/3' }}>
                {image.path && (
                  <Image
                    src={image.path}
                    alt={image.filename || `Page ${originalIndex + 1}`}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover"
                  />
                )}

                {/* Light overlay for subtle contrast */}
                <div className="absolute inset-0 bg-gradient-to-t from-white/40 via-transparent to-transparent" />

                {/* Edit Button - Top Right */}
                <Button
                  variant="default"
                  size="icon"
                  className={`absolute top-2 right-2 h-7 w-7 rounded-lg shadow-md backdrop-blur-sm ${
                    isEditMode
                      ? 'bg-primary hover:bg-primary/90 border border-white/50'
                      : 'opacity-40 cursor-not-allowed bg-muted/90'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!isEditMode) {
                      toast.error(
                        'Edit mode required to change scale and dimensions',
                      );
                      return;
                    }
                    setCurrentImageForScaleEdit(image as ImageType);
                    setCurrentPageNumberForModal(originalIndex + 1);
                    setIsEditScaleModalOpen(true);
                  }}
                  aria-label={
                    isEditMode
                      ? 'Edit scale and dimensions'
                      : 'Edit scale and dimensions (Edit mode required)'
                  }
                  title={
                    isEditMode
                      ? 'Edit scale and dimensions'
                      : 'Edit mode required'
                  }
                >
                  <Pencil className="h-3.5 w-3.5" />
                </Button>

                {/* Title Overlay - Top Left */}
                <div className="absolute top-2 left-2 right-12">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 border border-border/50 shadow-sm">
                    <div className="text-sm font-semibold text-foreground leading-tight truncate">
                      Page {originalIndex + 1}:{' '}
                      {image.filename?.replace(/\.[^/.]+$/, '') ||
                        'Untitled Sheet'}
                    </div>
                  </div>
                </div>

                {/* Scale and Dimensions Overlay - Bottom */}
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1.5 border border-border/50 shadow-sm">
                    <div className="flex flex-col space-y-1 text-xs">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-muted-foreground">
                          Scale
                        </span>
                        {image.scale ? (
                          <span className="font-mono text-foreground bg-background px-2 py-0.5 rounded border border-border">
                            {image.scale.num_metric} {image.scale.num_unit} :{' '}
                            {image.scale.den_metric.toFixed(2)}{' '}
                            {image.scale.den_unit}
                          </span>
                        ) : (
                          <span className="italic text-muted-foreground bg-muted px-2 py-0.5 rounded">
                            Not set
                          </span>
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-muted-foreground">
                          Size
                        </span>
                        {image.dimensions ? (
                          <span className="font-mono text-foreground bg-background px-2 py-0.5 rounded border border-border">
                            {image.dimensions.width}" ×{' '}
                            {image.dimensions.height}"
                          </span>
                        ) : (
                          <span className="font-mono text-foreground bg-background px-2 py-0.5 rounded border border-border">
                            {DEFAULT_PAPER_SIZE.width}" ×{' '}
                            {DEFAULT_PAPER_SIZE.height}"
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
        {(!filteredImages || filteredImages.length === 0) && !isLoading && (
          <div className="p-6 text-center text-sm text-muted-foreground rounded-xl border border-dashed border-border/60 bg-muted/20 shadow-sm">
            <div className="space-y-2">
              <div className="text-xl text-muted-foreground/60">📄</div>
              <div className="font-medium">
                {searchQuery?.trim()
                  ? 'No pages match your search in this file.'
                  : 'No images found for this file.'}
              </div>
              <div className="text-xs text-muted-foreground/70">
                {searchQuery?.trim()
                  ? 'Try a different search term.'
                  : 'Images will appear here once they are processed.'}
              </div>
            </div>
          </div>
        )}
      </div>

      {currentImageForScaleEdit && (
        <EditScaleModal
          isOpen={isEditScaleModalOpen}
          onClose={() => {
            setIsEditScaleModalOpen(false);
            setCurrentImageForScaleEdit(null); // Clear image when closing
          }}
          image={currentImageForScaleEdit}
          pageNumber={currentPageNumberForModal}
          onSave={handleSave} // Updated to handleSave
          isSaving={isUpdating} // Updated to isUpdating
        />
      )}
    </SidebarMenuItem>
  );
}
