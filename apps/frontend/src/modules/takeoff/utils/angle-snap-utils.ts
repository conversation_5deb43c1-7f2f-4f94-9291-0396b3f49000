/**
 * Utility functions for angle snapping in point-to-point drawing tools
 * Provides functionality to snap drawing angles to 45-degree increments (0°, 45°, 90°, 135°, 180°, 225°, 270°, 315°)
 */

/**
 * Calculate the angle in degrees between two points
 * @param x1 X coordinate of the first point
 * @param y1 Y coordinate of the first point
 * @param x2 X coordinate of the second point
 * @param y2 Y coordinate of the second point
 * @returns Angle in degrees (0-360)
 */
export function calculateAngle(
  x1: number,
  y1: number,
  x2: number,
  y2: number,
): number {
  const deltaX = x2 - x1;
  const deltaY = y2 - y1;

  // Calculate angle using atan2 (returns -π to π radians)
  let angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

  // Normalize to 0-360 degrees
  if (angle < 0) {
    angle += 360;
  }

  return angle;
}

/**
 * Calculate the distance between two points
 * @param x1 X coordinate of the first point
 * @param y1 Y coordinate of the first point
 * @param x2 X coordinate of the second point
 * @param y2 Y coordinate of the second point
 * @returns Distance between the points
 */
export function calculateDistance(
  x1: number,
  y1: number,
  x2: number,
  y2: number,
): number {
  const deltaX = x2 - x1;
  const deltaY = y2 - y1;
  return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
}

/**
 * Snap an angle to the nearest 45-degree increment
 * @param angle Angle in degrees (0-360)
 * @returns Snapped angle in degrees (0, 45, 90, 135, 180, 225, 270, or 315)
 */
export function snapAngleTo45Degrees(angle: number): number {
  // Define the 8 cardinal and diagonal directions (45-degree increments)
  const snapAngles = [0, 45, 90, 135, 180, 225, 270, 315];

  let closestAngle = snapAngles[0];
  let minDifference = Math.abs(angle - snapAngles[0]);

  // Handle the wrap-around case for 0/360 degrees
  const wrapAroundDifference = Math.abs(angle - 360);
  if (wrapAroundDifference < minDifference) {
    minDifference = wrapAroundDifference;
    closestAngle = 0;
  }

  // Find the closest snap angle
  for (const snapAngle of snapAngles) {
    const difference = Math.abs(angle - snapAngle);
    if (difference < minDifference) {
      minDifference = difference;
      closestAngle = snapAngle;
    }
  }

  return closestAngle;
}

/**
 * Calculate a new point position based on a starting point, angle, and distance
 * @param startX X coordinate of the starting point
 * @param startY Y coordinate of the starting point
 * @param angle Angle in degrees
 * @param distance Distance from the starting point
 * @returns New point coordinates
 */
export function calculatePointFromAngle(
  startX: number,
  startY: number,
  angle: number,
  distance: number,
): { x: number; y: number } {
  // Convert angle to radians
  const radians = angle * (Math.PI / 180);

  // Calculate new coordinates
  const x = startX + distance * Math.cos(radians);
  const y = startY + distance * Math.sin(radians);

  return { x, y };
}

/**
 * Main function to snap a point to 90-degree constraints relative to a previous point
 * This is the primary function used by the drawing tool
 * @param lastX X coordinate of the previous point
 * @param lastY Y coordinate of the previous point
 * @param currentX X coordinate of the current mouse position
 * @param currentY Y coordinate of the current mouse position
 * @returns Snapped point coordinates that maintain distance but snap to 45-degree angles
 */
export function snapPointTo90Degrees(
  lastX: number,
  lastY: number,
  currentX: number,
  currentY: number,
): { x: number; y: number } {
  // Handle edge case: if mouse is exactly on the last point, return the last point
  if (lastX === currentX && lastY === currentY) {
    return { x: currentX, y: currentY };
  }

  // Calculate the original angle and distance
  const originalAngle = calculateAngle(lastX, lastY, currentX, currentY);
  const originalDistance = calculateDistance(lastX, lastY, currentX, currentY);

  // Handle edge case: if distance is very small (less than 1 pixel), return current position
  if (originalDistance < 1) {
    return { x: currentX, y: currentY };
  }

  // Snap the angle to the nearest 45-degree increment
  const snappedAngle = snapAngleTo45Degrees(originalAngle);

  // Calculate the new point using the snapped angle and original distance
  const snappedPoint = calculatePointFromAngle(
    lastX,
    lastY,
    snappedAngle,
    originalDistance,
  );

  return snappedPoint;
}

/**
 * Utility function to check if two angles are approximately equal within a tolerance
 * Useful for testing and debugging
 * @param angle1 First angle in degrees
 * @param angle2 Second angle in degrees
 * @param tolerance Tolerance in degrees (default: 1)
 * @returns True if angles are approximately equal
 */
export function anglesApproximatelyEqual(
  angle1: number,
  angle2: number,
  tolerance: number = 1,
): boolean {
  const difference = Math.abs(angle1 - angle2);
  // Handle wrap-around case (e.g., 359° and 1° should be considered close)
  const wrapAroundDifference = Math.abs(difference - 360);
  return Math.min(difference, wrapAroundDifference) <= tolerance;
}

/**
 * Get a human-readable description of a snapped angle direction
 * Useful for debugging and potential UI feedback
 * @param angle Angle in degrees
 * @returns Direction description
 */
export function getAngleDirection(angle: number): string {
  const normalizedAngle = angle % 360;

  switch (normalizedAngle) {
    case 0:
      return 'Right (0°)';
    case 45:
      return 'Down-Right (45°)';
    case 90:
      return 'Down (90°)';
    case 135:
      return 'Down-Left (135°)';
    case 180:
      return 'Left (180°)';
    case 225:
      return 'Up-Left (225°)';
    case 270:
      return 'Up (270°)';
    case 315:
      return 'Up-Right (315°)';
    default:
      return `${normalizedAngle}°`;
  }
}
