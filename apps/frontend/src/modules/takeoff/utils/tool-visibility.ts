import { SelectedTool } from '../types/drawing-types';
import { ComponentItemData } from '../components/CollapsibleComponentSection';

/**
 * Utility function to determine which tools should be visible based on selected component
 */
export const getVisibleTools = (
  selectedComponent: ComponentItemData | null,
): SelectedTool[] => {
  if (!selectedComponent) {
    // No component selected - this is Measure Mode
    // Show pan, select, comment, arrow, and all drawing tools for measurement
    return [
      'pan',
      'select',
      'comment',
      'arrow',
      'rectangle',
      'circle',
      'ellipse',
      'freehand',
      'curve',
      'point-to-point',
    ];
  }

  // Component is selected - show tools based on component type
  if (selectedComponent.geometryType === 'surface') {
    return [
      'pan',
      'select',
      'rectangle',
      'circle',
      'ellipse',
      'freehand',
      'curve',
      'point-to-point',
    ];
  }

  if (selectedComponent.geometryType === 'edge') {
    return ['pan', 'select', 'freehand', 'curve', 'point-to-point'];
  }

  // Point component
  return ['pan', 'select', 'point'];
};
