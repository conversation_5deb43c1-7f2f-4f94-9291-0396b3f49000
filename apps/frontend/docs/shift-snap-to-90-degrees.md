# Shift + Snap to 90 Degrees Implementation Plan

## Overview

Implement shift key functionality for point-to-point drawing tool that constrains the current line segment to 90-degree angles (horizontal, vertical, or diagonal) relative to the previous point, similar to tools like Figma.

## Current Implementation Analysis

### Existing Components

1. **useShiftKey.ts** - Already tracks shift key state globally
2. **useDrawingTools.ts** - Contains point-to-point drawing logic
3. **useTakeoffStore.ts** - Manages `isShiftPressed` state
4. **DrawingLayer.tsx** - Renders point-to-point lines

### Key Code Locations

- **Mouse move handler**: `useDrawingTools.ts` lines 559-575 (point-to-point preview)
- **Shift state**: Available via `isShiftPressed` from store
- **Preview points**: `currentDrawing.previewPoints` array

## Implementation Strategy

### 1. Angle Calculation Utility Functions

Create utility functions to:

- Calculate angle between two points
- Snap angle to nearest 45-degree increment (0°, 45°, 90°, 135°, 180°, 225°, 270°, 315°)
- Calculate new point position based on snapped angle and distance

### 2. Core Logic Flow

When shift is pressed during point-to-point drawing:

1. Get the last confirmed point from `currentDrawing.points`
2. Calculate current mouse position
3. Calculate angle from last point to mouse position
4. Snap angle to nearest 45-degree increment
5. Calculate distance from last point to mouse position
6. Generate new snapped position using snapped angle and original distance
7. Update preview points with snapped position

### 3. Implementation Details

#### A. Utility Functions (new file: `angle-snap-utils.ts`)

```typescript
// Calculate angle in degrees between two points
function calculateAngle(x1: number, y1: number, x2: number, y2: number): number;

// Snap angle to nearest 45-degree increment
function snapAngleTo45Degrees(angle: number): number;

// Calculate new point position based on angle and distance
function calculatePointFromAngle(
  startX: number,
  startY: number,
  angle: number,
  distance: number,
): { x: number; y: number };

// Main function to snap point to 90-degree constraint
function snapPointTo90Degrees(
  lastX: number,
  lastY: number,
  currentX: number,
  currentY: number,
): { x: number; y: number };
```

#### B. Modify Mouse Move Handler

In `useDrawingTools.ts`, update the point-to-point mouse move logic:

```typescript
if (currentDrawing.type === 'point-to-point' && currentDrawing.isDrawing) {
  let targetX = pos.x;
  let targetY = pos.y;

  // Apply shift constraint if shift is pressed and we have at least one point
  if (isShiftPressed && currentDrawing.points.length >= 2) {
    const lastPointIndex = currentDrawing.points.length - 2;
    const lastX = currentDrawing.points[lastPointIndex];
    const lastY = currentDrawing.points[lastPointIndex + 1];

    const snappedPoint = snapPointTo90Degrees(lastX, lastY, pos.x, pos.y);
    targetX = snappedPoint.x;
    targetY = snappedPoint.y;
  }

  // Create preview with snapped or original position
  const completePreviewPoints = [...currentDrawing.points, targetX, targetY];

  // Rest of existing logic...
}
```

### 4. Mathematical Implementation

#### Angle Calculation

```typescript
function calculateAngle(
  x1: number,
  y1: number,
  x2: number,
  y2: number,
): number {
  const deltaX = x2 - x1;
  const deltaY = y2 - y1;
  let angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

  // Normalize to 0-360 degrees
  if (angle < 0) {
    angle += 360;
  }

  return angle;
}
```

#### Angle Snapping

```typescript
function snapAngleTo45Degrees(angle: number): number {
  const snapAngles = [0, 45, 90, 135, 180, 225, 270, 315];

  let closestAngle = snapAngles[0];
  let minDifference = Math.abs(angle - snapAngles[0]);

  for (const snapAngle of snapAngles) {
    const difference = Math.abs(angle - snapAngle);
    if (difference < minDifference) {
      minDifference = difference;
      closestAngle = snapAngle;
    }
  }

  return closestAngle;
}
```

#### Point Calculation

```typescript
function calculatePointFromAngle(
  startX: number,
  startY: number,
  angle: number,
  distance: number,
): { x: number; y: number } {
  const radians = angle * (Math.PI / 180);
  const x = startX + distance * Math.cos(radians);
  const y = startY + distance * Math.sin(radians);

  return { x, y };
}
```

### 5. Visual Feedback Considerations

#### Current Preview System

- Point-to-point drawings show dashed preview lines
- Preview updates in real-time during mouse movement
- No visual changes needed - existing preview will show snapped lines

#### Potential Enhancements (Future)

- Different line style when snapped (e.g., thicker, different color)
- Angle indicator tooltip
- Snap guides/grid overlay

### 6. Edge Cases to Handle

1. **First Point**: No snapping needed (no previous point)
2. **Zero Distance**: Handle case where mouse is exactly on last point
3. **Multiple Points**: Always snap relative to the immediate previous point
4. **Surface vs Edge**: Same logic applies to both geometry types

### 7. Testing Strategy

#### Manual Testing Scenarios

1. Draw point-to-point line without shift - should behave normally
2. Draw point-to-point line with shift held - should snap to 45-degree increments
3. Release shift mid-drawing - should return to free movement
4. Press shift mid-drawing - should immediately snap current preview
5. Test with both edge and surface components
6. Test with multiple points in sequence

#### Key Behaviors to Verify

- Smooth transition between snapped and free movement
- Correct angle calculations for all quadrants
- Proper distance preservation when snapping
- No interference with other drawing tools
- No interference with existing shift functionality (multi-select)

### 8. Implementation Steps

1. **Create angle-snap-utils.ts** with utility functions
2. **Modify useDrawingTools.ts** mouse move handler
3. **Test basic functionality** with simple cases
4. **Refine edge cases** and mathematical precision
5. **Test integration** with existing features
6. **Performance optimization** if needed

### 9. Files to Modify

1. **New file**: `apps/frontend/src/modules/takeoff/utils/angle-snap-utils.ts`
2. **Modify**: `apps/frontend/src/modules/takeoff/hooks/useDrawingTools.ts`
3. **Test**: Verify no conflicts with existing shift key usage

### 10. Backward Compatibility

- No breaking changes to existing functionality
- Shift key behavior for multi-select remains unchanged
- Point-to-point drawing without shift remains identical
- All existing drawing tools unaffected

## Success Criteria

1. ✅ Holding shift during point-to-point drawing constrains to 90-degree angles
2. ✅ Releasing shift returns to free movement immediately
3. ✅ Angle snapping works in all directions (8 cardinal/diagonal directions)
4. ✅ Distance from last point is preserved during snapping
5. ✅ No interference with other tools or shift key functionality
6. ✅ Smooth, responsive user experience matching Figma-like behavior

## Future Enhancements

- Visual snap indicators
- Configurable snap angles (30°, 15°, etc.)
- Snap to existing line angles
- Grid-based snapping
- Angle display tooltip
